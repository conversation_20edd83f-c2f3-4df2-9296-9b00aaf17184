import config from '../config/config.js';
import { sendDataWithRetries } from '../network/sendData.js';
import { handleFailedBatch } from '../helpers/persistFailed.js';
import { state } from '../state.js';

/**
 * Adds data points to the unified batch queue
 * @param {Array} dataPoints - Array of data points to add
 * @param {string} source - Source of data ('live' or 'csv')
 */
export function addDataToBatch(dataPoints, source = 'unknown') {
    if (!Array.isArray(dataPoints)) {
        dataPoints = [dataPoints];
    }

    // Add metadata to each data point about its source
    const enrichedDataPoints = dataPoints.map(point => ({
        data: point,
        source: source,
        addedAt: Date.now()
    }));

    state.unifiedBatchState.pendingDataQueue.push(...enrichedDataPoints);

    // Update counters
    if (source === 'live') {
        state.unifiedBatchState.liveDataCount += dataPoints.length;
    } else if (source === 'csv') {
        state.unifiedBatchState.csvDataCount += dataPoints.length;
    }

    console.log(`Added ${dataPoints.length} ${source} data points to batch queue. Queue size: ${state.unifiedBatchState.pendingDataQueue.length}`);
}

/**
 * Processes and sends batches from the unified queue
 * @returns {Object} Result of batch processing
 */
export async function processBatchQueue() {
    if (!state.initData) {
        console.error("UNIFIED: Cannot process batches without initialization data.");
        return { success: false, reason: 'missing_init_data' };
    }

    const now = Date.now();
    const timeSinceLastBatch = state.unifiedBatchState.lastBatchSentTime
        ? now - state.unifiedBatchState.lastBatchSentTime
        : config.unifiedMode.batchSendInterval;

    // Check if it's time to send a batch
    if (timeSinceLastBatch < config.unifiedMode.batchSendInterval && state.unifiedBatchState.pendingDataQueue.length < config.unifiedMode.maxBatchSize) {
        return { success: true, reason: 'waiting_for_interval_or_batch_size' };
    }

    if (state.unifiedBatchState.pendingDataQueue.length === 0) {
        return { success: true, reason: 'no_data_to_send' };
    }

    // Extract data points for sending (up to maxBatchSize)
    const batchSize = Math.min(config.unifiedMode.maxBatchSize, state.unifiedBatchState.pendingDataQueue.length);
    const batchToSend = state.unifiedBatchState.pendingDataQueue.splice(0, batchSize);

    // Extract just the data points (remove metadata)
    const dataPoints = batchToSend.map(item => item.data);

    // Sort by timestamp to maintain chronological order
    dataPoints.sort((a, b) => {
        const timestampA = Array.isArray(a) ? a[0] : a.timestamp || 0;
        const timestampB = Array.isArray(b) ? b[0] : b.timestamp || 0;
        return timestampA - timestampB;
    });

    // Find the latest timestamp in this batch and track CSV vs live separately
    let latestTimestampInBatch = state.lastSentTimestamp;
    let latestCsvTimestampInBatch = state.catchupProgress.lastCsvTimestampSent;

    batchToSend.forEach((item, index) => {
        const timestamp = Array.isArray(item.data) ? item.data[0] : item.data.timestamp || 0;
        if (timestamp > latestTimestampInBatch) {
            latestTimestampInBatch = timestamp;
        }

        // Track CSV timestamps separately
        if (item.source === 'csv' && timestamp > latestCsvTimestampInBatch) {
            latestCsvTimestampInBatch = timestamp;
        }
    });

    // Count sources in this batch for logging
    const sourceCount = batchToSend.reduce((acc, item) => {
        acc[item.source] = (acc[item.source] || 0) + 1;
        return acc;
    }, {});

    state.unifiedBatchState.totalBatchesSent++;
    console.log(`UNIFIED: Sending batch #${state.unifiedBatchState.totalBatchesSent} with ${dataPoints.length} points. Sources: ${JSON.stringify(sourceCount)}. Latest timestamp: ${latestTimestampInBatch}, Latest CSV timestamp: ${latestCsvTimestampInBatch}`);

    // Debug: Log first few data points to see what's actually being sent
    console.log(`UNIFIED DEBUG: First 3 data points being sent:`);
    dataPoints.slice(0, 3).forEach((point, index) => {
        const timestamp = Array.isArray(point) ? point[0] : 'unknown';
        const values = Array.isArray(point) ? point.slice(1, 4).join(', ') : 'unknown';
        console.log(`  Point ${index + 1}: timestamp=${timestamp}, values=[${values}...]`);
    });

    // Send the batch
    const sendResult = await sendDataWithRetries(dataPoints);

    if (sendResult.success) {
        state.lastSentTimestamp = latestTimestampInBatch;
        state.catchupProgress.lastCsvTimestampSent = latestCsvTimestampInBatch;
        state.unifiedBatchState.lastBatchSentTime = now;
        console.log(`UNIFIED: Batch sent successfully. New lastSentTimestamp: ${state.lastSentTimestamp}, CSV timestamp: ${state.catchupProgress.lastCsvTimestampSent}. Queue remaining: ${state.unifiedBatchState.pendingDataQueue.length}`);
        return {
            success: true,
            batchSize: dataPoints.length,
            newTimestamp: latestTimestampInBatch,
            newCsvTimestamp: latestCsvTimestampInBatch,
            sourceCount: sourceCount
        };
    } else {
        console.error("UNIFIED: Failed to send batch. Adding back to failed buffer.");
        // Add the failed batch to the failed batches system
        handleFailedBatch(dataPoints, state.initData.metadata.mac);
        return {
            success: false,
            reason: 'send_failed',
            error: sendResult.error
        };
    }
}

/**
 * Gets statistics about the current batch queue
 * @returns {Object} Queue statistics
 */
export function getBatchQueueStats() {
    return {
        queueSize: state.unifiedBatchState.pendingDataQueue.length,
        totalBatchesSent: state.unifiedBatchState.totalBatchesSent,
        liveDataCount: state.unifiedBatchState.liveDataCount,
        csvDataCount: state.unifiedBatchState.csvDataCount,
        lastBatchSentTime: state.unifiedBatchState.lastBatchSentTime,
        timeSinceLastBatch: state.unifiedBatchState.lastBatchSentTime
            ? Date.now() - state.unifiedBatchState.lastBatchSentTime
            : null
    };
}

/**
 * Clears the batch queue (useful for testing or error recovery)
 */
export function clearBatchQueue() {
    const clearedCount = state.unifiedBatchState.pendingDataQueue.length;
    state.unifiedBatchState.pendingDataQueue = [];
    console.log(`UNIFIED: Cleared ${clearedCount} items from batch queue.`);
    return clearedCount;
}
