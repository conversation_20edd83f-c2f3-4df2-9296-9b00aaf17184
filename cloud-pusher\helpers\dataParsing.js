function parseCsvLineToTypedValues(line, metadata) {
    const values = line.split(',');
    const timestamp = parseInt(values[0], 10);
    if (isNaN(timestamp)) return null;
    const typedValues = [timestamp];

    for (let i = 1; i < values.length; i++) {
        const registerIndex = i - 1;
        const value = values[i];

        if (value === 'null' || value === '') {
            typedValues.push(null);
            continue;
        }

        if (registerIndex < metadata.registers.length) {
            const register = metadata.registers[registerIndex];
            switch (register.dtype) {
                case 'F32': case 'F64': typedValues.push(parseFloat(value)); break;
                case 'B': typedValues.push(value === 'true' || value === '1'); break;
                case 'STR': typedValues.push(value); break;
                default: typedValues.push(parseFloat(value)); break;
            }
        } else {
            const numValue = parseFloat(value);
            typedValues.push(isNaN(numValue) ? value : numValue);
        }
    }
    return typedValues;
}
export default parseCsvLineToTypedValues;