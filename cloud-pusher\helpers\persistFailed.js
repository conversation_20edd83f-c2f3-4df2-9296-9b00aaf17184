import fs from 'fs';
import config from '../config/config.js';
import { state } from '../state.js';

function loadPersistedFailedBatches() {
    try {
        if (fs.existsSync(config.failedBatchesFilePath)) {
            const data = fs.readFileSync(config.failedBatchesFilePath, 'utf8');
            if (data) {
                const parsed = JSON.parse(data);
                if (Array.isArray(parsed)) {
                    state.inMemoryFailedBatches = parsed;
                    console.log(`Loaded ${state.inMemoryFailedBatches.length} failed batches from disk.`);
                }
            }
        }
    } catch (error) {
        console.error(`Error loading persisted failed batches: ${error.message}`);
    }
}

function persistFailedBatches() {
    try {
        if (state.inMemoryFailedBatches.length > 0) {
            fs.writeFileSync(
                config.failedBatchesFilePath,
                JSON.stringify(state.inMemoryFailedBatches),
                'utf8'
            );
            console.log(`Persisted ${state.inMemoryFailedBatches.length} failed batches to disk.`);
        } else if (fs.existsSync(config.failedBatchesFilePath)) {
            fs.unlinkSync(config.failedBatchesFilePath);
            console.log('Removed empty failed batches file.');
        }
    } catch (error) {
        console.error(`Error persisting failed batches: ${error.message}`);
    }
}

function handleFailedBatch(batch, mac) {
    console.warn(`Handling failed batch for MAC: ${mac || "unknown"}`);
    
    if (state.inMemoryFailedBatches.length >= config.failedBatchBufferLimit) {
        console.warn(`Failed batch buffer limit (${config.failedBatchBufferLimit}) reached. Persisting to disk.`);
        persistFailedBatches();
    }
    
    state.inMemoryFailedBatches.push({
        timestamp: Date.now(),
        data: batch,
        mac: mac || (state.initData?.metadata?.mac || "unknown"),
        endpoint: 'data'
    });
}

export { loadPersistedFailedBatches, persistFailedBatches, handleFailedBatch };
