import winston from 'winston';
import path from 'path';
import fs from 'fs';

// --- Configuration Constants ---

const SERVICE_NAME = 'device-api';
const LOGS_DIR = path.join(process.cwd(), 'data', 'logs');
const DEFAULT_LOG_LEVEL = process.env.LOG_LEVEL || 'info';

// Custom log levels and colors (consistent with original)
const LOG_LEVELS = { error: 0, warn: 1, info: 2, http: 3, debug: 4, trace: 5 };
const LOG_COLORS = { error: 'red', warn: 'yellow', info: 'green', http: 'magenta', debug: 'blue', trace: 'grey' };

// Standard file rotation settings
const BASE_FILE_ROTATION_CONFIG = {
  maxsize: 5 * 1024 * 1024, // 5MB
};

// --- Setup ---

// Ensure log directory exists
fs.mkdirSync(LOGS_DIR, { recursive: true });

// Apply custom colors to <PERSON>
winston.addColors(LOG_COLORS);

const deviceLogFormatter = winston.format.printf(
  ({ level, message, timestamp, mac, deviceTime, response, stack, metadata }) => {
    // Base log entry
    let logEntry = `${timestamp} [${level}]`;

    // Add device identifiers if present
    if (mac) logEntry += ` [MAC:${mac}]`;
    if (deviceTime) logEntry += ` [DeviceTime:${deviceTime}]`;

    // Add the main log message
    logEntry += `: ${message}`;

    // response format: 'ServerTime,DataTime,reserved' 
    if (response !== undefined) {
      const match = typeof response === 'string' && response.match(/^(\d+),(\d+),\d+$/);
      if (match) {
        logEntry += ` Response: [ServerTime:${match[1]}, DataTime:${match[2]}]`;
      } else {
        logEntry += ` Response: ${typeof response === 'object' ? JSON.stringify(response) : response}`;
      }
    }

    // Add remaining metadata, excluding potentially large/sensitive common keys
    // This 'metadata' object is populated by winston.format.metadata()
    if (metadata && Object.keys(metadata).length > 0) {
      const filteredMeta = { ...metadata };
      delete filteredMeta.headers; // Example of filtering
      delete filteredMeta.payload;
      delete filteredMeta.body;
      // Any other fields passed in the third argument to logger.log(), etc.,
      // and not in fillExcept of winston.format.metadata(), will be here.

      if (Object.keys(filteredMeta).length > 0) {
        logEntry += ` ${JSON.stringify(filteredMeta)}`;
      }
    }

    // Append stack trace if available (added by winston.format.errors)
    if (stack) {
      logEntry += `\nStack: ${stack}`;
    }

    return logEntry;
  }
);

// --- Logger Transports ---

const consoleTransport = new winston.transports.Console({
  format: winston.format.combine(
    winston.format.colorize({ all: true }), // Apply colors for console output
    deviceLogFormatter                 // Use the same custom formatter
  ),
  level: DEFAULT_LOG_LEVEL, // Console log level can also be controlled by env var
});

const httpLogsFileTransport = new winston.transports.File({
  filename: path.join(LOGS_DIR, `${SERVICE_NAME}-http.log`),
  level: 'http',  // Logs 'http' and more severe levels (info, warn, error)
  ...BASE_FILE_ROTATION_CONFIG,
  maxFiles: 2,    // Specific to http logs
});

const errorLogsFileTransport = new winston.transports.File({
  filename: path.join(LOGS_DIR, `${SERVICE_NAME}-error.log`),
  level: 'error', // Only logs 'error' level
  ...BASE_FILE_ROTATION_CONFIG,
  maxFiles: 2,    // Specific to error logs
});

// --- Logger Creation ---

const logger = winston.createLogger({
  levels: LOG_LEVELS,
  level: DEFAULT_LOG_LEVEL,
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    winston.format.errors({ stack: true }), // Ensures stack trace is captured from Error objects
    winston.format.metadata({               // Gathers extra data into a 'metadata' field
      fillExcept: ['message', 'level', 'timestamp', 'label', 'mac', 'deviceTime', 'response', 'service', 'stack', 'error']
    }),
    deviceLogFormatter                      // Apply the main custom formatting logic
  ),
  defaultMeta: { service: SERVICE_NAME },   // Default metadata added to all logs
  transports: [
    consoleTransport,
    errorLogsFileTransport,
    httpLogsFileTransport,
  ],
  exitOnError: false, // Recommended: Do not exit on logger errors in production
});

// --- Helper Functions (Maintaining original API and logic) ---

/**
 * Logs standard device operations.
 * Uses 'http' level for successful operations (metadata.success is not false),
 * and 'info' for failures (metadata.success === false).
 */
export const logDeviceData = (mac, deviceTime, message, metadata = {}) => {
  const level = metadata.success === false ? 'info' : 'http';
  logger.log(level, message, { mac, deviceTime, ...metadata });
};

/**
 * Logs device-related errors.
 * The actual error object should be passed to allow Winston to process it.
 */
export const logDeviceError = (mac, deviceTime, message, errorObject, metadata = {}) => {
  logger.error(message, {
    mac,
    deviceTime,
    error: errorObject || new Error('Unknown error occurred'), // Pass the actual error object
    ...metadata
  });
};

/**
 * Logs device responses.
 * Typically uses 'http' level for standard interactions.
 */
export const logDeviceResponse = (mac, deviceTime, message, response, metadata = {}) => {
  logger.http(message, { mac, deviceTime, response, ...metadata });
};

// --- Exports ---

export default logger; // Export the configured logger instance
