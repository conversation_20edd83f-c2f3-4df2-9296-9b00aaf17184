import config from '../config/config.js';
import { sendDataWithRetries } from '../network/sendData.js';
import { handleFailedBatch } from '../helpers/persistFailed.js';
import { getRegisterValidationInfo, liveDataGenerators } from './liveDataGen.js';
import { STATES, state } from '../state.js';

async function runLiveIteration() {
    if (!state.initData) {
        console.error("LIVE: Cannot run live mode without initialization data.");
        state.currentGlobalState = STATES.HANDSHAKE_FAILED; // re-initialize
        return { newTimestamp: state.lastSentTimestamp };
    }

    const generatorFn = liveDataGenerators[config.liveMode.source];
    if (!generatorFn) {
        console.error(`LIVE: Unknown live data source: ${config.liveMode.source}. Switching to IDLE.`);
        state.currentGlobalState = STATES.IDLE;
        if (state.tickServiceCancel) state.tickServiceCancel();
        return { newTimestamp: state.lastSentTimestamp };
    }

    if (!state.liveModeRuntimeState.registerValidation) {
        state.liveModeRuntimeState.registerValidation = await getRegisterValidationInfo(state.initData.metadata);
        state.liveModeRuntimeState.lastValues = [null, ...state.initData.metadata.registers.map((_reg, idx) =>
            state.liveModeRuntimeState.registerValidation[idx]?.defaultValue
        )];
    }


    let currentBatch = [];
    let latestTimestampInBatch = state.lastSentTimestamp;

    // Use liveMode.interval to decide if we should generate a batch now.
    // The tickService runs more frequently (serviceIntervalMs).
    // This check makes live data generation adhere to its own configured interval.
    const now = Date.now();
    if (!state.liveModeRuntimeState.lastBatchSentTime || (now - state.liveModeRuntimeState.lastBatchSentTime >= config.liveMode.interval)) {
        console.log(`LIVE: Generating a new batch of ${config.liveMode.batchSize} live data points.`);
        for (let i = 0; i < config.liveMode.batchSize; i++) {
            const { dataPoint, newLastValues } = await generatorFn(
                state.initData.metadata,
                state.liveModeRuntimeState.lastValues,
                state.liveModeRuntimeState.registerValidation,
                config.liveMode
            );
            currentBatch.push(dataPoint);
            state.liveModeRuntimeState.lastValues = newLastValues; 
            if (dataPoint && typeof dataPoint[0] === 'number') {
                latestTimestampInBatch = Math.max(latestTimestampInBatch, dataPoint[0]);
            }
        }

        if (currentBatch.length > 0) {
            state.liveModeRuntimeState.batchCount++;
            console.log(`LIVE: Sending batch #${state.liveModeRuntimeState.batchCount} (${currentBatch.length} points). Newest timestamp: ${latestTimestampInBatch}`);
            const sendResult = await sendDataWithRetries(state.initData.metadata.mac, currentBatch);
            if (sendResult.success) {
                state.lastSentTimestamp = latestTimestampInBatch;
                console.log(`LIVE: Batch sent. New lastSentTimestamp: ${state.lastSentTimestamp}`);
                state.liveModeRuntimeState.lastBatchSentTime = Date.now(); 
            } else {
                console.error("LIVE: Failed to send live batch. Adding to failed buffer.");
                handleFailedBatch(currentBatch, state.initData.metadata.mac);
                return { newTimestamp: state.lastSentTimestamp };
            }
        }
    } else {
        console.log(`LIVE: Waiting for live interval (${config.liveMode.interval}ms). Time since last batch: ${now - (state.liveModeRuntimeState.lastBatchSentTime || now)}ms`);
    }
    return { newTimestamp: state.lastSentTimestamp };
}

export default runLiveIteration
