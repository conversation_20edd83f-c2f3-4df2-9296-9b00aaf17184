import fs from 'fs';
import config from '../config/config.js';
import { addDataToBatch } from './batchProcessor.js';
import parseCsvLineToTypedValues from '../helpers/dataParsing.js';
import { STATES, state } from '../state.js';

/**
 * Processes CSV files and adds data to the unified batch queue
 * @returns {Object} Result of CSV processing
 */
async function processCsvDataForUnified() {
    if (!state.initData) {
        console.error("UNIFIED CATCHUP: Cannot process CSV data without initialization data.");
        return { success: false, reason: 'missing_init_data' };
    }

    // Check if all catchup data has been processed
    if (state.catchupProgress.allCatchupDataSent) {
        return { success: true, reason: 'all_catchup_complete', isComplete: true };
    }

    // Check if we have files to process
    if (state.catchupProgress.currentFileIndex >= state.catchupProgress.fileQueue.length) {
        console.log("UNIFIED CATCHUP: All CSV files processed.");
        state.catchupProgress.allCatchupDataSent = true;
        return { success: true, reason: 'all_files_processed', isComplete: true };
    }

    const currentFile = state.catchupProgress.fileQueue[state.catchupProgress.currentFileIndex];

    let fileContent;
    try {
        fileContent = fs.readFileSync(currentFile.path, 'utf8');
    } catch (e) {
        console.error(`UNIFIED CATCHUP: Error reading file ${currentFile.path}: ${e.message}`);
        // Move to next file
        state.catchupProgress.currentFileIndex++;
        state.catchupProgress.currentLineInFile = 0;
        return { success: false, reason: 'file_read_error', error: e.message };
    }

    const lines = fileContent.trim().split('\n');
    const dataPoints = [];
    let linesProcessedInThisCall = 0;
    const maxLinesToProcess = config.unifiedMode.csvProcessingRate;

    // Process lines from current position
    for (let i = state.catchupProgress.currentLineInFile;
         i < lines.length && linesProcessedInThisCall < maxLinesToProcess;
         i++) {

        const line = lines[i];
        linesProcessedInThisCall++;

        if (!line.trim()) continue;

        const typedValues = parseCsvLineToTypedValues(line, state.initData.metadata);
        if (!typedValues) {
            console.warn(`UNIFIED CATCHUP: Skipping invalid CSV line in ${currentFile.name}: ${line}`);
            continue;
        }

        const timestamp = typedValues[0];

        // Only include CSV data newer than what we've already sent from CSV files
        // Use separate CSV timestamp tracking to avoid conflicts with live data
        if (timestamp > state.catchupProgress.lastCsvTimestampSent) {
            dataPoints.push(typedValues);
        }
    }

    // Update line pointer for next iteration
    state.catchupProgress.currentLineInFile += linesProcessedInThisCall;

    // Add processed data to the unified batch queue
    if (dataPoints.length > 0) {
        const firstTimestamp = dataPoints[0][0];
        const lastTimestamp = dataPoints[dataPoints.length - 1][0];
        addDataToBatch(dataPoints, 'csv');
        console.log(`UNIFIED CATCHUP: Processed ${linesProcessedInThisCall} lines from ${currentFile.name}, added ${dataPoints.length} valid data points to queue (timestamps: ${firstTimestamp} to ${lastTimestamp}, lastCsvSent: ${state.catchupProgress.lastCsvTimestampSent})`);
    } else {
        console.log(`UNIFIED CATCHUP: Processed ${linesProcessedInThisCall} lines from ${currentFile.name}, no new data points added (lastCsvSent: ${state.catchupProgress.lastCsvTimestampSent})`);
    }

    // Check if current file is complete
    if (state.catchupProgress.currentLineInFile >= lines.length) {
        console.log(`UNIFIED CATCHUP: Finished processing file ${currentFile.name}`);
        state.catchupProgress.currentFileIndex++;
        state.catchupProgress.currentLineInFile = 0;

        // Check if all files are now complete
        if (state.catchupProgress.currentFileIndex >= state.catchupProgress.fileQueue.length) {
            state.catchupProgress.allCatchupDataSent = true;
            console.log("UNIFIED CATCHUP: All CSV files have been processed.");
            return {
                success: true,
                reason: 'all_files_complete',
                isComplete: true,
                dataPointsProcessed: dataPoints.length,
                linesProcessed: linesProcessedInThisCall
            };
        }
    }

    return {
        success: true,
        reason: 'processing_ongoing',
        isComplete: false,
        dataPointsProcessed: dataPoints.length,
        linesProcessed: linesProcessedInThisCall,
        currentFile: currentFile.name,
        fileProgress: `${state.catchupProgress.currentLineInFile}/${lines.length}`,
        filesRemaining: state.catchupProgress.fileQueue.length - state.catchupProgress.currentFileIndex
    };
}

export default processCsvDataForUnified;
