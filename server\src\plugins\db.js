import fp from 'fastify-plugin';
import Knex from 'knex';
import knexConfig from '../../knexfile.js';

async function dbPlugin(fastify, options) {
    const env = process.env.NODE_ENV || 'development';
    const knex = Knex(knexConfig[env]);

    // Run migrations
    try {
        await knex.migrate.latest();
        fastify.log.info('Database migrations completed');
    } catch (err) {
        fastify.log.error('Error running migrations:', err);
        throw err;
    }

    fastify.decorate('knex', knex);

    // Close database connection when server stops
    fastify.addHook('onClose', async (instance) => {
        await instance.knex.destroy();
        fastify.log.info('Database connection closed');
    });
}

export default fp(dbPlugin, {
    name: 'db-plugin'
});
