import path from 'path';
import fs from 'fs';
import fetch from 'node-fetch';
import config from '../config/config.js';

// --- Initialization, Data Sending ---

async function performHandshake() {
    try {
        console.log('Attempting device registration (handshake)...');
        const metadataPath = path.join(config.dataDir, 'metadata.json');
        if (!fs.existsSync(metadataPath)) {
            console.error(`FATAL: Metadata file not found at ${metadataPath}.`);
            return { success: false, error: 'Metadata file not found' };
        }
        const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));

        if (!metadata.mac || !metadata.model || !Array.isArray(metadata.registers)) {
            console.error('FATAL: Metadata is invalid (missing mac, model, or registers).');
            return { success: false, error: 'Invalid metadata structure' };
        }
        console.log(`Device MAC: ${metadata.mac}, Model: ${metadata.model}`);

        const response = await fetch(`${config.server.baseUrl}/data`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(metadata),
            timeout: config.networkTimeout,
        });
        const responseText = await response.text();
        console.log(`Handshake Response (${response.status}):`, responseText);

        if (!response.ok) {
            throw new Error(`Handshake failed with status ${response.status}: ${responseText}`);
        }

        const parts = responseText.split(',');
        if (parts.length < 2) {
            throw new Error('Invalid handshake response data format. Expected "serverTime,dataTime".');
        }
        const serverTime = parts[0];
        const dataTime = parts[1];
        const latestTimestampFromServer = parseInt(dataTime, 10);

        if (isNaN(latestTimestampFromServer)) {
            throw new Error(`Invalid latest data timestamp from server: ${dataTime}`);
        }

        console.log(`Handshake successful. ServerTime: ${serverTime}, DataTime: ${latestTimestampFromServer}`);
        return { success: true, metadata, serverTime, latestTimestampFromServer };
    } catch (error) {
        console.error(`Handshake error: ${error.message}`);
        return { success: false, error: error.message };
    }
}
export default performHandshake;
