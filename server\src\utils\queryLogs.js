import fs from 'fs';
import path from 'path';
import { createResponse } from './stdResponse.js';

const queryLogs = async function (request, reply) {

    try {
        const {
            type = 'http',       // log type: 'http', 'error'
            lines = 100,         // number of lines to return
            search = '',         // optional search term
            mac = '',            // filter by MAC address
            from = '',           // start date (YYYY-MM-DD)
            to = '',              // end date (YYYY-MM-DD)
            level = ''            // filter by log level
        } = request.query;

        // Validate parameters
        const maxLines = 1000;
        const lineCount = Math.min(parseInt(lines) || 100, maxLines);

        const logsDir = path.join(process.cwd(), '/data/logs');
        const logFile = type === 'error'
            ? path.join(logsDir, 'device-api-error.log')
            : path.join(logsDir, 'device-api-http.log');

        if (!fs.existsSync(logFile)) {
            return reply.code(404).send(createResponse({
                errors: [`Log file ${type} not found`]
            }));
        }


        const fileContent = fs.readFileSync(logFile, 'utf8');
        let logLines = fileContent.split('\n').filter(line => line.trim() !== '');

        if (search) {
            logLines = logLines.filter(line => line.includes(search));
        }

        if (mac) {
            logLines = logLines.filter(line => line.includes(`[MAC:${mac}]`));
        }

        if (from) {
            const fromDate = new Date(from);
            logLines = logLines.filter(line => {
                const lineDate = new Date(line.substring(0, 19));
                return lineDate >= fromDate;
            });
        }

        if (to) {
            const toDate = new Date(to);
            toDate.setHours(23, 59, 59, 999); 
            logLines = logLines.filter(line => {
                const lineDate = new Date(line.substring(0, 19));
                return lineDate <= toDate;
            });
        }

        if (level) {
            logLines = logLines.filter(line => {
                const levelRegex = new RegExp(`\\[${level.toUpperCase()}\\]`, 'i');
                return levelRegex.test(line);
            });
        }

        const resultLines = logLines.slice(-lineCount);

        return reply.code(200).send(createResponse({
            data: resultLines,
            other: {
                total: logLines.length,
                returned: resultLines.length,
                type: type
            }
        }));

    } catch (error) {
        fastify.log.error('Error retrieving logs', error);
        return reply.code(500).send(createResponse({
            errors: ['Failed to retrieve logs']
        }));
    }
}

export default queryLogs