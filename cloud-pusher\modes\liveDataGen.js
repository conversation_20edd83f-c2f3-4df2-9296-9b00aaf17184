import config from '../config/config.js';
import { state } from '../state.js';

// --- Live Data Generation ---
async function getRegisterValidationInfo(metadata) {
    const registerValidation = {};
    metadata.registers.forEach((register, index) => {
        const dtype = register.dtype?.toUpperCase() || 'UNKNOWN';
        const name = register.register || register.name || `register_${index}`;
        registerValidation[index] = {
            name,
            dtype,
            validationType: getValidationType(dtype),
            defaultValue: getDefaultValueForType(dtype, name)
        };
    });
    return registerValidation;
}

function getValidationType(dtype) { 
    if (!dtype) return 'ANY';
    switch (dtype.toUpperCase()) {
        case 'INT': case 'I32': case 'I16': case 'I8': return 'INTEGER';
        case 'FLOAT': case 'F32': case 'F64': return 'FLOAT';
        case 'STR': case 'STRING': return 'STRING';
        case 'B': case 'BOOL': case 'BOOLEAN': return 'BOOLEAN';
        default: return 'ANY';
    }
}

function getDefaultValueForType(dtype, name) { 
    if (!dtype) return 0;
    const lowerName = name.toLowerCase();
    switch (dtype.toUpperCase()) {
        case 'INT': case 'I32': case 'I16': case 'I8':
            if (lowerName.includes('temp')) return 25;
            if (lowerName.includes('humid')) return 50;
            return 0;
        case 'FLOAT': case 'F32': case 'F64':
            if (lowerName.includes('temp')) return 25.0;
            if (lowerName.includes('humid')) return 50.0;
            return 0.0;
        case 'STR': case 'STRING': return '';
        case 'B': case 'BOOL': case 'BOOLEAN': return false;
        default: return 0;
    }
}

function generateSimulatedLiveDataPoint(metadata, currentLastValues, registerMeta, liveConfig) {
    const timestamp = Math.floor(Date.now() / 1000);
    const dataPoint = [timestamp];
    const updatedLastValues = [...currentLastValues];

    metadata.registers.forEach((_register, index) => {
        if (updatedLastValues.length <= index + 1) {
            updatedLastValues.push(null);
        }

        const validation = registerMeta[index];
        if (!validation) {
            console.error(`No validation info for register ${index} in simulator`);
            dataPoint.push(null);
            return;
        }

        let lastValForRegister = updatedLastValues[index + 1];
        if (lastValForRegister === null || typeof lastValForRegister === 'undefined') {
            lastValForRegister = validation.defaultValue;
        }

        let newValue;
        const { variations } = liveConfig;
        switch (validation.validationType) {
            case 'INTEGER':
                const intVariation = Math.max(1, Math.abs(Math.round(lastValForRegister * variations.numeric)));
                newValue = Math.round(lastValForRegister + (Math.random() * intVariation * 2 - intVariation));
                break;
            case 'FLOAT':
                const floatVariation = Math.abs(lastValForRegister * variations.numeric);
                newValue = parseFloat((lastValForRegister + (Math.random() * floatVariation * 2 - floatVariation)).toFixed(2));
                break;
            case 'BOOLEAN':
                newValue = Math.random() < variations.boolean ? !lastValForRegister : lastValForRegister;
                break;
            case 'STRING':
                newValue = lastValForRegister;
                break;
            default:
                newValue = String(lastValForRegister);
                break;
        }
        dataPoint.push(newValue);
        updatedLastValues[index + 1] = newValue;
    });
    return { dataPoint, newLastValues: updatedLastValues };
}

const liveDataGenerators = {
    simulator: generateSimulatedLiveDataPoint,
    device: async (metadata, _lastValues, _registerValidation, _liveConfig) => {
        console.warn("Live data source 'device' is not implemented. Returning empty point.");
        const timestamp = Math.floor(Date.now() / 1000);
        const dataPoint = [timestamp, ...metadata.registers.map(() => null)];
        return { dataPoint, newLastValues: _lastValues };
    }
};

export { getRegisterValidationInfo, generateSimulatedLiveDataPoint, liveDataGenerators };
