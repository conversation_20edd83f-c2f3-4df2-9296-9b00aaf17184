/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function up(knex) {
    return knex.schema.table('users', table => {
        table.renameColumn('organization', 'company_name');
    });
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */

export function down(knex) {
    return knex.schema.table('users', table => {
        table.renameColumn('company_name', 'organization');
    });
}


