### Test device data post with nested array format
POST http://127.0.0.1:9001/api/device/data
Content-Type: application/json
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952300

[
  [ 1400435085, 36.25, 45.5, 1013.1, 66, true, "normal", 85.0],
  [ 1400435086, 36.25, 45.5, 1013.1, 67, 100, "normal", 84.5]
]

### Test device data post with CSV format
POST http://127.0.0.1:9001/api/device/data
Content-Type: text/csv
mac: AA:BB:CC:DD:EE:FF
dev-time: 1745952400

1400435090,25.9,45.5,1013.1,66,1,normal,85.0
1400435091,26.0,45.6,1013.0,67,0,warning,84.5
1400435092,26.0,45.7,

### Test handshake
POST http://pvapi1.netmeter.cloud/api/device/data
Content-Type: application/json

{
  "mac": "AA:BB:CC:DD:EE:FF",
  "model": "ENV-SENSOR-01",
  "registers": [
    {
      "register": "temperature",
      "dtype": "f32",
      "units": "°C",
      "group": "Environment",
      "index": 0
    },
    {
      "register": "humidity",
      "dtype": "f322",
      "units": "%",
      "group": "Environment",
      "index": 1
    },
    {
      "register": "pressure",
      "dtype": "f32",
      "units": "hPa",
      "group": "Environment",
      "index": 2
    },
    {
      "register": "light",
      "dtype": "i32",
      "units": "lux",
      "group": "Environment",
      "index": 3
    },
    {
      "register": "motion",
      "dtype": "bool",
      "units": "",
      "group": "Security",
      "index": 4
    },
    {
      "register": "status",
      "dtype": "str",
      "units": "",
      "group": "System",
      "index": 5
    },
    {
      "register": "battery_level",
      "dtype": "f32",
      "units": "%",
      "group": "System",
      "index": 6
    }
  ]
}

### Get metadata history
GET http://pvapi1.netmeter.cloud/api/device/meta-history/AA:BB:CC:DD:EE:FF


### Test logs endpoint
GET http://127.0.0.1:9001/api/device/logs?type=error&lines=5