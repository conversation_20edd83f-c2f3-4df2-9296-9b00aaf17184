import fp from 'fastify-plugin'
import fastifyJwt from '@fastify/jwt'
import fastifyAuth from '@fastify/auth'
import * as dotenv from 'dotenv'
dotenv.config()
import { readFileSync } from 'fs'; 
import { join } from 'path'; 


// const jwtSecret = readFileSync('./server/config/jwt-secret.conf', 'utf8').trim();
// console.log(jwtSecret)


async function authPlugin(fastify, opts) {

    fastify.register(fastifyJwt, {
        secret: process.env.JWT_SECRET
    })

    fastify.register(fastifyAuth)

    fastify.decorate('verifyJWT', async (request, reply) => {
        try {
            await request.jwtVerify()
        } catch (error) {
            if (error.code === 'FST_JWT_NO_AUTHORIZATION_IN_HEADER') {
                reply.code(401).send({ error: 'Authorization header is missing' })
            } else if (error.code=== 'FST_JWT_AUTHORIZATION_TOKEN_EXPIRED'){
                reply.code(401).send({ error: 'Authorization token expired' })
            } else if (error.code==='FST_JWT_AUTHORIZATION_TOKEN_INVALID'){
                reply.code(401).send({ error: 'Invalid Token' })
            }
            else {
                fastify.log(error) 
                reply.code(500).send({ error: 'Internal Server Error' })
            }
        }
    })
}

export default fp(authPlugin)



