// Simple test to run unified mode with debugging
import config from './config/config.js';

// Override config for testing
config.mode = 'unified';
config.serviceIntervalMs = 2000; // Slower for debugging
config.unifiedMode.batchSendInterval = 3000;
config.unifiedMode.csvProcessingRate = 10; // Process fewer lines per tick
config.liveMode.batchSize = 1;

console.log('Starting unified mode test with config:', {
    mode: config.mode,
    serviceIntervalMs: config.serviceIntervalMs,
    batchSendInterval: config.unifiedMode.batchSendInterval,
    csvProcessingRate: config.unifiedMode.csvProcessingRate
});

// Import and run the main cloud pusher
import('./index.js').catch(console.error);
