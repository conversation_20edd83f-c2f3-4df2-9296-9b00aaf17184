/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */

export async function up(knex) {
    return knex.schema.alterTable('devices', table => {
        // remove the 'oid' notNullable constraint
        table.integer('oid').unsigned().references('oid').inTable('organizations').onDelete('CASCADE').alter()
        // remove the 'nchan' notNullable constraint
        table.integer('nchan').alter()
    })
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */

export async function down(knex) {
    return knex.schema.alterTable('devices', table => {
        // Revert 'oid' and 'nchan' to notNullable (restore original constraint)
        table.integer('oid').unsigned().notNullable().references('oid').inTable('organizations').onDelete('CASCADE').alter()
        table.integer('nchan').notNullable().alter()
    });
}