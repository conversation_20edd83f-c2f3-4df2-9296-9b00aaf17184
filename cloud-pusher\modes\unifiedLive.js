import config from '../config/config.js';
import { addDataToBatch } from './batchProcessor.js';
import { getRegisterValidationInfo, liveDataGenerators } from './liveDataGen.js';
import { STATES, state } from '../state.js';

/**
 * Generates live data and adds it to the unified batch queue
 * @returns {Object} Result of live data generation
 */
async function generateLiveDataForUnified() {
    if (!state.initData) {
        console.error("UNIFIED LIVE: Cannot generate live data without initialization data.");
        return { success: false, reason: 'missing_init_data' };
    }

    const generatorFn = liveDataGenerators[config.liveMode.source];
    if (!generatorFn) {
        console.error(`UNIFIED LIVE: Unknown live data source: ${config.liveMode.source}`);
        return { success: false, reason: 'unknown_data_source' };
    }

    // Initialize live mode runtime state if needed
    if (!state.liveModeRuntimeState.registerValidation) {
        state.liveModeRuntimeState.registerValidation = await getRegisterValidationInfo(state.initData.metadata);
        state.liveModeRuntimeState.lastValues = [null, ...state.initData.metadata.registers.map((_reg, idx) =>
            state.liveModeRuntimeState.registerValidation[idx]?.defaultValue
        )];
    }

    const now = Date.now();
    const timeSinceLastGeneration = state.liveModeRuntimeState.lastBatchSentTime
        ? now - state.liveModeRuntimeState.lastBatchSentTime
        : config.unifiedMode.liveDataInterval;

    // Check if it's time to generate new live data
    if (timeSinceLastGeneration < config.unifiedMode.liveDataInterval) {
        return {
            success: true,
            reason: 'waiting_for_interval',
            timeRemaining: config.unifiedMode.liveDataInterval - timeSinceLastGeneration
        };
    }

    // Generate live data points with timestamps that continue from CSV data
    const dataPoints = [];

    // For unified mode, use timestamps that continue from the latest CSV timestamp
    // This ensures live data doesn't have timestamps far in the future compared to CSV data
    const liveConfigForUnified = {
        ...config.liveMode,
        useRelativeTimestamps: true, // This will make live data continue from lastSentTimestamp
        // Override interval to ensure reasonable timestamp progression
        interval: 60000 // 1 minute intervals for live data
    };

    for (let i = 0; i < config.liveMode.batchSize; i++) {
        const { dataPoint, newLastValues } = await generatorFn(
            state.initData.metadata,
            state.liveModeRuntimeState.lastValues,
            state.liveModeRuntimeState.registerValidation,
            liveConfigForUnified
        );

        if (dataPoint) {
            dataPoints.push(dataPoint);
            state.liveModeRuntimeState.lastValues = newLastValues;
        }
    }

    if (dataPoints.length > 0) {
        // Debug: Show sample of live data being added
        console.log(`UNIFIED LIVE DEBUG: Sample live data being added:`);
        dataPoints.forEach((point, index) => {
            const values = point.slice(1, 4).join(', ');
            console.log(`  Live Point ${index + 1}: timestamp=${point[0]}, values=[${values}...]`);
        });

        // Add generated data to the unified batch queue
        addDataToBatch(dataPoints, 'live');
        state.liveModeRuntimeState.lastBatchSentTime = now;
        state.liveModeRuntimeState.batchCount++;

        console.log(`UNIFIED LIVE: Generated ${dataPoints.length} live data points (batch #${state.liveModeRuntimeState.batchCount})`);

        return {
            success: true,
            dataPointsGenerated: dataPoints.length,
            batchCount: state.liveModeRuntimeState.batchCount
        };
    } else {
        console.warn("UNIFIED LIVE: No valid data points generated");
        return { success: false, reason: 'no_valid_data_generated' };
    }
}

export default generateLiveDataForUnified;
