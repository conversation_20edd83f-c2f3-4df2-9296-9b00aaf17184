/**
 * Extracts the domain part from an email address
 * @param {string} email - The email address
 * @returns {string|null} - The domain part of the email or null if invalid
 */
export function extractDomainFromEmail(email) {
    if (!email || typeof email !== 'string') {
        return null;
    }
    
    const parts = email.split('@');
    if (parts.length !== 2) {
        return null;
    }
    
    return parts[1].toLowerCase();
}

/**
 * Finds an organization by email domain
 * @param {object} db - Database connection
 * @param {string} email - User's email address
 * @returns {Promise<object|null>} - Organization object or null if not found
 */
export async function findOrganizationByEmailDomain(db, email) {
    const domain = extractDomainFromEmail(email);
    if (!domain) {
        return null;
    }
    
    return await db('organizations')
        .where({ email_domain: domain })
        .first();
}

/**
 * Assigns a user to an organization based on email domain
 * @param {object} db - Database connection
 * @param {number} uid - User ID
 * @param {string} email - User's email address
 * @returns {Promise<object|null>} - Result of the assignment or null if no matching organization
 */
export async function assignUserToOrgByEmailDomain(db, uid, email) {
    const org = await findOrganizationByEmailDomain(db, email);
    if (!org) {
        return null;
    }
    
    const defaultRole = 0;  // default role: disabled(0)
    
    const existingAssignment = await db('users_organizations')
        .where({ uid, oid: org.oid })
        .first();
    
    if (existingAssignment) {
        // user is already assigned to this organization
        return { 
            status: 'already_assigned',
            organization: org,
            role: existingAssignment.role
        };
    }
    
    // assign user to the organization
    await db('users_organizations').insert({
        uid,
        oid: org.oid,
        role: defaultRole
    });
    
    return {
        status: 'assigned',
        organization: org,
        role: defaultRole
    };
}
