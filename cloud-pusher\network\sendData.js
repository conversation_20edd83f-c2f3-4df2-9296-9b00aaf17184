
import fetch from 'node-fetch';
import config from '../config/config.js';
import sleep from '../helpers/sleep.js';
import { state } from '../state.js';
import {persistFailedBatches} from '../helpers/persistFailed.js'

async function sendDataWithRetries(data, endpoint = 'data') {
    if (!data || data.length === 0) {
        console.log("Skipping send for empty batch.");
        return { success: true, status: 204, data: "No Content" };
    }
    
    const requestTimestamp = Math.floor(Date.now() / 1000);
    let attempts = 0;
    let macAddress;
    
    // Extract MAC address based on data format
    if (data.mac) {
        // If data has a mac property directly
        macAddress = data.mac;
    } else if (data.data && data.data.length > 0) {
        // If data has a nested data array
        macAddress = data.mac;
    } else if (state.initData && state.initData.metadata) {
        // Fallback to state's metadata
        macAddress = state.initData.metadata.mac;
    } else {
        console.error("Cannot determine MAC address for data batch");
        return { success: false, error: "Missing MAC address", status: 400 };
    }
    
    while (attempts <= config.retryOptions.maxRetries) {
        try {
            attempts++;
            let contentType, body;
            
            // Prepare the body based on format
            if (config.format === 'json-array') {
                contentType = 'application/json';
                // If data already has the expected structure, use it directly
                if (data.data) {
                    body = JSON.stringify(data);
                } else {
                    // Otherwise, wrap the data array
                    body = JSON.stringify({
                        mac: macAddress,
                        data: Array.isArray(data) ? data : [data]
                    });
                }
            } else if (config.format === 'csv') {
                contentType = 'text/csv';
                if (Array.isArray(data)) {
                    body = data.map(row => Array.isArray(row) ? row.join(',') : row).join('\n');
                } else if (data.data && Array.isArray(data.data)) {
                    body = data.data.map(row => Array.isArray(row) ? row.join(',') : row).join('\n');
                } else {
                    body = String(data);
                }
            } else {
                throw new Error(`Unsupported data format: ${config.format}`);
            }

            console.log(`Sending data to ${config.server.baseUrl}/${endpoint} with MAC: ${macAddress}`);
            
            const response = await fetch(`${config.server.baseUrl}/${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': contentType,
                    'mac': macAddress,
                    'dev-time': requestTimestamp.toString(),
                },
                body,
                timeout: config.networkTimeout,
            });

            const responseText = await response.text();
            if (!response.ok) {
                throw new Error(`Server responded with status ${response.status}: ${responseText}`);
            }
            
            console.log(`Batch sent successfully for MAC ${macAddress} (Attempt ${attempts}). Response: ${responseText.substring(0, 100)}`);
            return { success: true, status: response.status, data: responseText };

        } catch (error) {
            console.warn(`Send data attempt ${attempts}/${config.retryOptions.maxRetries + 1} for MAC ${macAddress} failed: ${error.message}`);
            if (attempts > config.retryOptions.maxRetries) {
                console.error(`Failed to send batch for MAC ${macAddress} after ${attempts} attempts.`);
                return { success: false, error: error.message, status: error.response?.status || 500 };
            }
            await sleep(config.retryOptions.retryDelay * attempts);
        }
    }
    
    return { success: false, error: `Max retries exceeded for sendData (MAC: ${macAddress})`, status: 500 };
}

async function processPersistedFailedBatchesRetry() {
    if (state.inMemoryFailedBatches.length === 0) {
        console.log("No persisted failed batches to process at this time.");
        return true;
    }

    console.log(`\nAttempting to resend ${state.inMemoryFailedBatches.length} previously failed batches...`);
    const stillFailingBatches = [];
    let allResentSuccessfully = true;

    for (const failedBatchEntry of [...state.inMemoryFailedBatches]) {
        console.log(`Retrying batch for MAC ${failedBatchEntry.mac || "unknown"} (originally failed at ${new Date(failedBatchEntry.timestamp).toISOString()})`);
        const sendResult = await sendDataWithRetries(failedBatchEntry.data, failedBatchEntry.endpoint);
        if (!sendResult.success) {
            stillFailingBatches.push(failedBatchEntry);
            allResentSuccessfully = false;
        }
        await sleep(config.interval);
    }

    state.inMemoryFailedBatches = stillFailingBatches;
    if (state.inMemoryFailedBatches.length > 0) {
        console.warn(`${state.inMemoryFailedBatches.length} batches failed to send after retry.`);
        persistFailedBatches();
    } else {
        console.log("All failed batches sent successfully or cleared.");
        persistFailedBatches();
    }
    return allResentSuccessfully;
}

export { sendDataWithRetries, processPersistedFailedBatchesRetry };
