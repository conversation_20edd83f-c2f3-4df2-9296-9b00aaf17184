import path from 'path'

// --- Configuration & Constants ---
const DEFAULT_CONFIG = {
    server: {
        host: '127.0.0.1',
        port: 9001,
        baseUrl: 'http://127.0.0.1:9001/api/device',
    },
    dataDir: './data',
    batchSize: 100,
    interval: 100, // ms - interval between sending batches (within a single tick if multiple batches)
    format: 'json-array', // json-array or csv
    retryOptions: {
        maxRetries: 3,
        retryDelay: 2000, // ms
    },
    networkTimeout: 30000,
    failedBatchBufferLimit: 50,
    failedBatchesFilePath: path.join(process.cwd(), 'failed_batches.json'),
    mode: 'unified', // catchup, live, both, or unified
    liveMode: {
        enabled: true,
        interval: 500,
        source: 'simulator',
        registers: null,
        baseValues: {},
        variations: {
            numeric: 0.05,
            boolean: 0.1,
        },
        batchSize: 1, // default 1 data point per live batch
    },
    // New unified mode configuration
    unifiedMode: {
        enabled: true,
        batchSendInterval: 1000, // ms - how often to send accumulated batches
        maxBatchSize: 100, // maximum data points per batch sent to server
        liveDataInterval: 500, // ms - how often to generate live data points
        csvProcessingRate: 50, // lines to process per tick from CSV files
    },
    serviceIntervalMs: 1000, // tickService run
};
export default DEFAULT_CONFIG;