import { state, STATES } from './state.js';
import { addDataToBatch, processBatchQueue } from './modes/batchProcessor.js';

// Mock initialization data for testing
const mockInitData = {
    metadata: {
        mac: '123456789012',
        registers: [
            { name: 'temperature', dtype: 'f64' },
            { name: 'humidity', dtype: 'f64' }
        ]
    },
    serverTime: Date.now(),
    latestTimestampFromServer: 1000000 // Set a baseline timestamp
};

// Test function to verify CSV timestamp tracking
async function testCsvTimestampTracking() {
    console.log('=== Testing CSV Timestamp Tracking ===\n');
    
    // Initialize state
    state.initData = mockInitData;
    state.lastSentTimestamp = mockInitData.latestTimestampFromServer;
    state.catchupProgress.lastCsvTimestampSent = mockInitData.latestTimestampFromServer;
    state.currentGlobalState = STATES.UNIFIED;
    
    console.log(`Initial state:`);
    console.log(`  lastSentTimestamp: ${state.lastSentTimestamp}`);
    console.log(`  lastCsvTimestampSent: ${state.catchupProgress.lastCsvTimestampSent}\n`);
    
    // Test 1: Add older CSV data (should be included)
    console.log('1. Adding older CSV data...');
    const oldCsvData = [
        [1000100, 20.1, 50.1], // Older than baseline but newer than lastCsvTimestampSent
        [1000200, 20.2, 50.2],
        [1000300, 20.3, 50.3]
    ];
    
    addDataToBatch(oldCsvData, 'csv');
    console.log(`Added ${oldCsvData.length} CSV data points`);
    
    // Test 2: Add newer live data
    console.log('\n2. Adding newer live data...');
    const liveData = [
        [Date.now(), 25.5, 60.2] // Much newer timestamp
    ];
    
    addDataToBatch(liveData, 'live');
    console.log(`Added ${liveData.length} live data points`);
    
    console.log(`\nQueue size: ${state.unifiedBatchState.pendingDataQueue.length}`);
    
    // Test 3: Process batch and check timestamp tracking
    console.log('\n3. Processing batch...');
    
    // Mock the sendDataWithRetries function
    const sendDataModule = await import('./network/sendData.js');
    const originalSendData = sendDataModule.sendDataWithRetries;
    
    sendDataModule.sendDataWithRetries = async (data) => {
        console.log(`Mock sending batch with ${data.length} data points:`);
        data.forEach((point, index) => {
            console.log(`  Point ${index + 1}: timestamp=${point[0]}, values=[${point.slice(1).join(', ')}]`);
        });
        return { success: true, status: 200, data: 'OK' };
    };
    
    // Force batch processing
    state.unifiedBatchState.lastBatchSentTime = 0;
    
    try {
        const result = await processBatchQueue();
        console.log('\nBatch processing result:', result);
        
        console.log(`\nUpdated state:`);
        console.log(`  lastSentTimestamp: ${state.lastSentTimestamp}`);
        console.log(`  lastCsvTimestampSent: ${state.catchupProgress.lastCsvTimestampSent}`);
        
        // Test 4: Add more CSV data to verify tracking works
        console.log('\n4. Adding more CSV data to test tracking...');
        const moreCsvData = [
            [1000150, 20.15, 50.15], // Should be filtered out (older than lastCsvTimestampSent)
            [1000350, 20.35, 50.35], // Should be included (newer than lastCsvTimestampSent)
            [1000400, 20.4, 50.4]    // Should be included
        ];
        
        addDataToBatch(moreCsvData, 'csv');
        console.log(`Added ${moreCsvData.length} more CSV data points to queue`);
        console.log(`Queue size after adding: ${state.unifiedBatchState.pendingDataQueue.length}`);
        
        // Check what's actually in the queue
        console.log('\nQueue contents:');
        state.unifiedBatchState.pendingDataQueue.forEach((item, index) => {
            const timestamp = Array.isArray(item.data) ? item.data[0] : 'unknown';
            console.log(`  Item ${index + 1}: source=${item.source}, timestamp=${timestamp}`);
        });
        
    } catch (error) {
        console.error('Error during batch processing:', error);
    } finally {
        // Restore original function
        sendDataModule.sendDataWithRetries = originalSendData;
    }
    
    console.log('\n=== CSV Timestamp Tracking Test Complete ===');
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testCsvTimestampTracking().catch(console.error);
}

export { testCsvTimestampTracking };
