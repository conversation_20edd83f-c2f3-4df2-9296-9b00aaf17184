/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function up(knex) {
    // SQLite doesn't support altering column types 
    // create a new table with the new schema, copy the data, and replace the old table

    // Step 1: Get the current table schema
    const tableInfo = await knex.raw('PRAGMA table_info(devices)');

    // Step 2: Create a new table with the same schema but with macid as BIGINT
    await knex.schema.createTable('devices_new', table => {
        // Add macid as BIGINT primary key
        table.bigInteger('macid').primary();

        // Add all other columns with their original definitions
        for (const column of tableInfo) {
            if (column.name !== 'macid') {
                const type = column.type.toLowerCase();

                // Create the column with the appropriate type
                if (type === 'integer') {
                    if (column.pk === 1) {
                        table.integer(column.name).primary();
                    } else {
                        const col = table.integer(column.name);
                        if (column.notnull === 1) col.notNullable();
                    }
                } else if (type.includes('varchar') || type === 'text') {
                    const col = table.string(column.name);
                    if (column.notnull === 1) col.notNullable();
                    if (column.unique === 1) col.unique();
                } else if (type === 'real' || type === 'float') {
                    const col = table.float(column.name);
                    if (column.notnull === 1) col.notNullable();
                } else if (type === 'json') {
                    const col = table.json(column.name);
                    if (column.notnull === 1) col.notNullable();
                } else {
                    // Default to string for unknown types
                    const col = table.string(column.name);
                    if (column.notnull === 1) col.notNullable();
                }
            }
        }
    });

    // Step 3: Get foreign key info
    const foreignKeys = await knex.raw('PRAGMA foreign_key_list(devices)');

    // Step 4: Add foreign key constraints to the new table
    if (foreignKeys.length > 0) {
        await knex.schema.alterTable('devices_new', table => {
            for (const fk of foreignKeys) {
                table.foreign(fk.from)
                    .references(fk.to)
                    .inTable(fk.table)
                    .onDelete(fk.on_delete || 'NO ACTION')
                    .onUpdate(fk.on_update || 'NO ACTION');
            }
        });
    }

    // Step 5: Copy data from the old table to the new table
    await knex.raw('INSERT INTO devices_new SELECT * FROM devices');

    // Step 6: Drop the old table
    await knex.schema.dropTable('devices');

    // Step 7: Rename the new table to the original name
    await knex.raw('ALTER TABLE devices_new RENAME TO devices');

    // Step 8: Get and recreate indexes
    const indexList = await knex.raw('PRAGMA index_list(devices)');
    for (const index of indexList) {
        if (!index.name.startsWith('sqlite_')) {
            const indexInfo = await knex.raw(`PRAGMA index_info(${index.name})`);
            const columns = indexInfo.map(ii => ii.name);

            if (index.unique) {
                await knex.schema.alterTable('devices', table => {
                    table.unique(columns, index.name);
                });
            } else {
                await knex.schema.alterTable('devices', table => {
                    table.index(columns, index.name);
                });
            }
        }
    }
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function down(knex) {
    // Revert the changes by converting back to INTEGER

    // Step 1: Get the current table schema
    const tableInfo = await knex.raw('PRAGMA table_info(devices)');

    // Step 2: Create a new table with the original schema (macid as INTEGER)
    await knex.schema.createTable('devices_old', table => {
        // Add macid as INTEGER primary key
        table.integer('macid').primary();

        // Add all other columns with their original definitions
        for (const column of tableInfo) {
            if (column.name !== 'macid') {
                const type = column.type.toLowerCase();

                // Create the column with the appropriate type
                if (type === 'integer') {
                    if (column.pk === 1) {
                        table.integer(column.name).primary();
                    } else {
                        const col = table.integer(column.name);
                        if (column.notnull === 1) col.notNullable();
                    }
                } else if (type.includes('varchar') || type === 'text') {
                    const col = table.string(column.name);
                    if (column.notnull === 1) col.notNullable();
                    if (column.unique === 1) col.unique();
                } else if (type === 'real' || type === 'float') {
                    const col = table.float(column.name);
                    if (column.notnull === 1) col.notNullable();
                } else if (type === 'json') {
                    const col = table.json(column.name);
                    if (column.notnull === 1) col.notNullable();
                } else {
                    // Default to string for unknown types
                    const col = table.string(column.name);
                    if (column.notnull === 1) col.notNullable();
                }
            }
        }
    });

    // Step 3: Get foreign key info
    const foreignKeys = await knex.raw('PRAGMA foreign_key_list(devices)');

    // Step 4: Add foreign key constraints to the new table
    if (foreignKeys.length > 0) {
        await knex.schema.alterTable('devices_old', table => {
            for (const fk of foreignKeys) {
                table.foreign(fk.from)
                    .references(fk.to)
                    .inTable(fk.table)
                    .onDelete(fk.on_delete || 'NO ACTION')
                    .onUpdate(fk.on_update || 'NO ACTION');
            }
        });
    }

    // Step 5: Copy data from the current table to the old table
    // Note: This might cause data loss for values that are too large for INTEGER
    await knex.raw('INSERT INTO devices_old SELECT * FROM devices');

    // Step 6: Drop the current table
    await knex.schema.dropTable('devices');

    // Step 7: Rename the old table to the original name
    await knex.raw('ALTER TABLE devices_old RENAME TO devices');

    // Step 8: Get and recreate indexes
    const indexList = await knex.raw('PRAGMA index_list(devices)');
    for (const index of indexList) {
        if (!index.name.startsWith('sqlite_')) {
            const indexInfo = await knex.raw(`PRAGMA index_info(${index.name})`);
            const columns = indexInfo.map(ii => ii.name);

            if (index.unique) {
                await knex.schema.alterTable('devices', table => {
                    table.unique(columns, index.name);
                });
            } else {
                await knex.schema.alterTable('devices', table => {
                    table.index(columns, index.name);
                });
            }
        }
    }
}