export function validateHandshake(mac, nchan, model) {
    // Validate required fields
    if (!mac) {
        return { 
            error: 'Missing required field',
            required: ['mac']
        };
    }

    // Validate MAC address format (XX:XX:XX:XX:XX:XX)
    const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
    if (!macRegex.test(mac)) {
        return { 
            error: 'Invalid MAC address format',
            expected: 'XX:XX:XX:XX:XX:XX'
        };
    }

    return null;
}