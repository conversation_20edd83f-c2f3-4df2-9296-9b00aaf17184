// Regex Patterns
const ONLY_LETTERS_REGEX = /^[a-zA-Z]+$/;
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const ALPHANUMERIC_REGEX = /^[a-zA-Z0-9]+$/;
const NUMBER_REGEX = /\d/g;
const LOWERCASE_REGEX = /[a-z]/g;
const UPPERCASE_REGEX = /[A-Z]/g;
const SPECIAL_CHAR_REGEX = /[\W_]/g;

// Validation Functions

function required(value) {
    return !!value || "Field is required";
}

function minLength(minLength) {
    return function (value) {
        return value.length >= minLength || `Must be at least ${minLength} characters`;
    };
}

function onlyLetters(value) {
    return ONLY_LETTERS_REGEX.test(value) || "Only letters are allowed";
}

function alphanumeric(value) {
    return ALPHANUMERIC_REGEX.test(value) || "Only alphanumeric characters allowed";
}

function validEmail(value) {
    return EMAIL_REGEX.test(value) || "Invalid email format";
}

function minNumbers(count) {
    return function (value) {
        return (value.match(NUMBER_REGEX) || []).length >= count || `Must contain at least ${count} number(s)`;
    };
}

function minLowercase(count) {
    return function (value) {
        return (value.match(LOWERCASE_REGEX) || []).length >= count || `Must contain at least ${count} lowercase letter(s)`;
    };
}

function minUppercase(count) {
    return function (value) {
        return (value.match(UPPERCASE_REGEX) || []).length >= count || `Must contain at least ${count} uppercase letter(s)`;
    };
}

function minSpecial(count) {
    return function (value) {
        return (value.match(SPECIAL_CHAR_REGEX) || []).length >= count || `Must contain at least ${count} special character(s)`;
    };
}



/**
 * Tests a value against a set of validation rules.
 * @param {Array<function(string): (true|string)>} rules - The validation rules to apply.
 * @param {string} value - The value to validate.
 * @returns {true|string} - True if all validations pass, otherwise an error message.
 */
function testRule(rules, value) {
    for (const rule of rules) {
        const result = rule(value);
        if (result !== true) {
            return result; // Return first failed validation message
        }
    }
    return true; // All validations passed
}


/**
 * Defines validation rules for different user input fields.
 * Each field has an array of validation functions that check specific criteria.
 */
const userRules = {
    name: [required, onlyLetters],
    password: [
        required,
        minLength(8),
        minNumbers(1),
        minLowercase(1),
        minUppercase(1),
        minSpecial(1)
    ],
    companyName: [required],
    email: [required, validEmail],
};

export { userRules, testRule }