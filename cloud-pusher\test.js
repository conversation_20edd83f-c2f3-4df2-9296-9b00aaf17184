import fs from 'fs';
import path from 'path';

/**
 * Test script to verify that the cloud pusher can read and parse data files correctly
 */

// Configuration
const config = {
  dataDir: './cloud-pusher/data',
  testFile: {
    coarse: 'DC19825.csv',
    fine: 'D19825.csv'
  }
};

/**
 * Main function to run the test
 */
async function main() {
  try {
    console.log('Starting Cloud Pusher Test');
    console.log('----------------------------------------');

    // Read metadata file
    const metadataPath = path.join(config.dataDir, 'metadata.json');
    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
    
    console.log(`Device MAC: ${metadata.mac}`);
    console.log(`Device Model: ${metadata.model}`);
    console.log(`Number of registers: ${metadata.registers.length}`);
    
    // Test reading coarse data file
    await testReadDataFile('coarse', config.testFile.coarse, metadata);
    
    // Test reading fine data file
    await testReadDataFile('fine', config.testFile.fine, metadata);
    
    console.log('Test completed successfully');
  } catch (error) {
    console.error('Error in test:', error);
  }
}

/**
 * Test reading a data file
 * @param {string} dirType - 'coarse' or 'fine'
 * @param {string} fileName - File name
 * @param {Object} metadata - Device metadata
 */
async function testReadDataFile(dirType, fileName, metadata) {
  const filePath = path.join(config.dataDir, dirType, fileName);
  console.log(`\nTesting ${dirType} data file: ${fileName}`);
  
  try {
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      return;
    }
    
    // Read file content
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const lines = fileContent.trim().split('\n');
    
    console.log(`File contains ${lines.length} lines`);
    
    if (lines.length === 0) {
      console.log('File is empty');
      return;
    }
    
    // Parse first line
    const firstLine = lines[0];
    const values = firstLine.split(',');
    
    console.log(`First line contains ${values.length} values`);
    console.log(`Timestamp: ${values[0]}`);
    
    // Parse values based on metadata
    const parsedValues = [];
    
    for (let i = 1; i < values.length; i++) {
      const registerIndex = i - 1;
      
      if (registerIndex < metadata.registers.length) {
        const register = metadata.registers[registerIndex];
        const value = values[i];
        
        let parsedValue;
        
        if (value === 'null' || value === '') {
          parsedValue = null;
        } else if (register.dtype === 'F32' || register.dtype === 'F64') {
          parsedValue = parseFloat(value);
        } else if (register.dtype === 'B') {
          parsedValue = value === 'true' || value === '1';
        } else if (register.dtype === 'STR') {
          parsedValue = value;
        } else {
          parsedValue = parseFloat(value);
        }
        
        parsedValues.push({
          register: register.register,
          dtype: register.dtype,
          value: parsedValue
        });
      }
    }
    
    // Print parsed values
    console.log('Parsed values:');
    parsedValues.forEach(({ register, dtype, value }) => {
      console.log(`  ${register} (${dtype}): ${value}`);
    });
    
    console.log(`Successfully parsed ${parsedValues.length} values from ${dirType} data file`);
  } catch (error) {
    console.error(`Error testing ${dirType} data file:`, error);
  }
}

// Run the main function
main().catch(console.error);




