import db from '../database/db.js';

export default async function getPermissions(uid) {
    // fetch oid and role for given uid
    const orgs = await db('users_organizations')
        .where({ uid })
        .select('oid', 'role')
        .then(rows => Object.fromEntries(rows.map(row => [row.oid, row.role])));

    // fetch macid and role for given uid
    const devices = await db('devices')
        .join('users_organizations', 'devices.oid', 'users_organizations.oid')
        .where('users_organizations.uid', uid)
        .andWhere('users_organizations.role', '>=', 1) // no read for disabled users
        .select('devices.macid', 'users_organizations.role')
        .then(rows => Object.fromEntries(
            rows.map(row => [row.macid, row.role >= 64 ? 1 : 0]) // 1 for orgadmin (modify), 0 for author and user (read-only)
        ))

        // Find users who share organizations with the current user
        const sharedUsers = await db('users_organizations as uo1')
            .join('users_organizations as uo2', 'uo1.oid', 'uo2.oid')
            .where('uo1.uid', uid)
            .whereNot('uo2.uid', uid) // Exclude the current user
            .select('uo2.uid')
            .distinct();

        const users = {};

        if (sharedUsers.length > 0) {
            // Get organizations where the user is an admin
            const adminOrgs = await db('users_organizations')
                .where('uid', uid)
                .andWhere('role', '>=', 64) // OrgAdmin role
                .pluck('oid');

            // Get users that the current user can manage 
            const manageableUsers = adminOrgs.length > 0 ?
                await db('users_organizations')
                    .whereIn('oid', adminOrgs)
                    .whereNot('uid', uid)
                    .pluck('uid')
                    .then(ids => new Set(ids)) :
                new Set();

            // Create the users object with permissions
            for (const user of sharedUsers) {
                users[user.uid] = manageableUsers.has(user.uid) ? 1 : 0; // 1 if manageable, 0 otherwise (read-only)
            }
        }
    return { users, orgs, devices }
}
