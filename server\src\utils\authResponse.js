import getPermissions from './getPermissions.js';

export default async function generateAuthResponse(user) {
    const isAdmin = user.admin === 1;
    const payload = {
        uid: user.uid,
        ...(isAdmin ? { admin: 1 } : await getPermissions(user.uid)),
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60 // 7 days expiry
    };

    const extendedUser = {
        first: user.first,
        last: user.last,
        email: user.email,
        uid: user.uid,
        ...(isAdmin ? { admin: 1 } : payload)
    };

    return { payload, extendedUser };
}

