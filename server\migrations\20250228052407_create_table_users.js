/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export function up(knex) {
    return knex.schema.createTable('users', table => {
        table.increments('uid').primary()
        table.string('first').notNullable()
        table.string('last').notNullable()
        table.string('organization')
        table.string('email').unique().notNullable()
        table.string('pass').notNullable()
        table.string('phone')
        table.integer('enable').defaultTo(1); // 1=enable, others=disabled
        table.integer('admin').defaultTo(0);  // 0=normal user, 1=admin

        table.boolean('registered').defaultTo(false)
    })
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export function down(knex) {
    return knex.schema.dropTable('users')
}