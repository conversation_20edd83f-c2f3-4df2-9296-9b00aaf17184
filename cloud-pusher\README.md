# Cloud Pusher

This tool pushes data from embedded systems to the Praevista Server. It reads data from CSV files in the data directory and sends it to the server using the device API.

## Features

- Device registration via handshake
- Data posting with JSON array or CSV format
- Processes both coarse and fine data files
- Configurable batch sizes and intervals
- Respects the latest data timestamp from the server
- Supports both historical data catchup and live data simulation
- **NEW: Unified mode for simultaneous live and CSV data processing**
- Failed batch handling with retry mechanism

## Directory Structure

The data directory should have the following structure:

```
data/
├── metadata.json
├── persist.json
├── coarse/
│   ├── DC19825.csv
│   ├── DC19826.csv
│   └── ...
└── fine/
    ├── D19825.csv
    ├── D19826.csv
    └── ...
```

- `metadata.json`: Contains device metadata including MAC address, model, and register definitions
- `persist.json`: Contains persistent device state
- `coarse/`: Directory containing coarse data files (lower frequency)
- `fine/`: Directory containing fine data files (higher frequency)

## Data Format

The CSV files should have the following format:

```
timestamp,value1,value2,value3,...
```

Where:
- `timestamp`: Unix timestamp in seconds
- `value1`, `value2`, etc.: Values for each register defined in the metadata

## Usage

You can run the cloud pusher with different modes:

```bash
# Run unified mode (simultaneous live and CSV processing) - DEFAULT
npm start

# Run both catchup and live modes (sequential)
node index.js --mode both

# Run only live data simulation
npm run live

# Run only historical data catchup
npm run catch

# Run unified mode with custom settings
node index.js --mode unified --unified-batch-interval 2000 --csv-processing-rate 100
```

Or customize the behavior using command-line arguments:

```bash
node index.js --server http://localhost:9001/api/device --data-dir ./data --batch-size 20 --interval 300 --mode unified
```

### Command-Line Options

- `--help`: Show help message
- `--server <url>`: Server base URL (default: http://127.0.0.1:9001/api/device)
- `--data-dir <path>`: Data directory (default: ./data)
- `--batch-size <number>`: Batch size (default: 100)
- `--interval <number>`: Interval between sending batches in ms (default: 300)
- `--format <format>`: Data format: json-array or csv (default: json-array)
- `--network-timeout <ms>`: Network timeout for API calls (default: 30000)
- `--failed-buffer-limit <num>`: In-memory limit for failed batches (default: 50)
- `--failed-batches-path <path>`: File path for persisted failed batches
- `--mode <mode>`: Operation mode: catchup, live, both, or unified (default: unified)
- `--live-interval <ms>`: Interval for generating live data batches (default: 500)
- `--live-source <source>`: Live data source: simulator or device (default: simulator)
- `--live-batch-size <number>`: Number of data points per batch in live mode (default: 1)
- `--service-interval <ms>`: Main service tick interval in ms (default: 1000)
- `--unified-batch-interval <ms>`: Interval for sending unified batches (default: 1000)
- `--unified-max-batch <number>`: Maximum data points per unified batch (default: 100)
- `--csv-processing-rate <num>`: Lines to process per tick from CSV files in unified mode (default: 50)

## Operation Modes

### Catchup Mode
Processes historical data from CSV files, sending data points with timestamps newer than the last timestamp acknowledged by the server.

### Live Mode
Simulates real-time data generation, creating new data points based on configurable parameters and sending them to the server.

### Both Mode
Runs catchup mode first to process historical data, then switches to live mode for ongoing data simulation.

### Unified Mode (NEW)
**Simultaneously processes both CSV files and generates live data**, eliminating data gaps that occur in sequential processing. Both data sources feed into a unified batch queue that sends data at regular intervals. This ensures continuous data flow without interruption when catching up on historical data while live data is being generated.

Key benefits:
- **No data gaps**: Live data continues to be generated while processing CSV files
- **Chronological ordering**: All data is sorted by timestamp before sending
- **Efficient batching**: Combines data from both sources into optimally-sized batches
- **Real-world simulation**: Mimics actual embedded system behavior where historical and live data coexist

## Timing Behavior

### Traditional Modes (catchup, live, both)
- `serviceIntervalMs`: Controls how often the main service loop runs (default: 1000ms)
- `interval`: Delay between sending consecutive batches within a single tick (default: 300ms)
- `liveMode.interval`: Minimum time between generating live data batches (default: 500ms)

### Unified Mode
- `serviceIntervalMs`: Controls how often the main service loop runs (default: 1000ms)
- `unifiedMode.batchSendInterval`: How often to send accumulated batches (default: 1000ms)
- `unifiedMode.liveDataInterval`: How often to generate live data points (default: 500ms)
- `unifiedMode.csvProcessingRate`: Lines to process per tick from CSV files (default: 50)
- `unifiedMode.maxBatchSize`: Maximum data points per batch sent to server (default: 100)

Note: In unified mode, data from both sources is continuously added to a queue and sent in batches at regular intervals, ensuring optimal throughput and no data gaps.

