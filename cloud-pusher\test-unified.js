import { state, STATES } from './state.js';
import { addDataToBatch, processBatchQueue, getBatchQueueStats } from './modes/batchProcessor.js';
import config from './config/config.js';

// Mock initialization data for testing
const mockInitData = {
    metadata: {
        mac: '123456789012',
        registers: [
            { name: 'temperature', dtype: 'f64' },
            { name: 'humidity', dtype: 'f64' },
            { name: 'pressure', dtype: 'f64' }
        ]
    },
    serverTime: Date.now(),
    latestTimestampFromServer: Date.now() - 10000
};

// Test function to simulate unified mode behavior
async function testUnifiedMode() {
    console.log('=== Testing Unified Mode ===\n');
    
    // Initialize state
    state.initData = mockInitData;
    state.lastSentTimestamp = mockInitData.latestTimestampFromServer;
    state.currentGlobalState = STATES.UNIFIED;
    
    console.log('1. Testing batch queue functionality...');
    
    // Test adding live data
    const liveData = [
        [Date.now(), 25.5, 60.2, 1013.25],
        [Date.now() + 1000, 25.7, 60.1, 1013.30],
        [Date.now() + 2000, 25.9, 59.8, 1013.35]
    ];
    
    addDataToBatch(liveData, 'live');
    console.log('Added live data to batch queue');
    
    // Test adding CSV data
    const csvData = [
        [Date.now() + 3000, 26.1, 59.5, 1013.40],
        [Date.now() + 4000, 26.3, 59.2, 1013.45]
    ];
    
    addDataToBatch(csvData, 'csv');
    console.log('Added CSV data to batch queue');
    
    // Check queue stats
    let stats = getBatchQueueStats();
    console.log('Queue stats after adding data:', stats);
    
    console.log('\n2. Testing batch processing...');
    
    // Force batch processing by setting last batch time to past
    state.unifiedBatchState.lastBatchSentTime = Date.now() - config.unifiedMode.batchSendInterval - 1000;
    
    // Mock the sendDataWithRetries function to avoid actual network calls
    const originalSendData = (await import('./network/sendData.js')).sendDataWithRetries;
    
    // Create a mock that simulates successful sending
    const mockSendData = async (data) => {
        console.log(`Mock sending ${Array.isArray(data) ? data.length : 1} data points`);
        return { success: true, status: 200, data: 'OK' };
    };
    
    // Replace the function temporarily
    const sendDataModule = await import('./network/sendData.js');
    sendDataModule.sendDataWithRetries = mockSendData;
    
    try {
        const result = await processBatchQueue();
        console.log('Batch processing result:', result);
        
        // Check updated stats
        stats = getBatchQueueStats();
        console.log('Queue stats after processing:', stats);
        
    } catch (error) {
        console.error('Error during batch processing:', error);
    } finally {
        // Restore original function
        sendDataModule.sendDataWithRetries = originalSendData;
    }
    
    console.log('\n3. Testing queue behavior with different intervals...');
    
    // Add more data
    addDataToBatch([[Date.now() + 5000, 26.5, 58.9, 1013.50]], 'live');
    addDataToBatch([[Date.now() + 6000, 26.7, 58.6, 1013.55]], 'csv');
    
    // Try processing immediately (should wait for interval)
    const immediateResult = await processBatchQueue();
    console.log('Immediate processing result (should wait):', immediateResult);
    
    stats = getBatchQueueStats();
    console.log('Final queue stats:', stats);
    
    console.log('\n=== Unified Mode Test Complete ===');
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testUnifiedMode().catch(console.error);
}

export { testUnifiedMode };
