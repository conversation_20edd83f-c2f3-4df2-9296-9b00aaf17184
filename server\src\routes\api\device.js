import { <PERSON>ce<PERSON>ontroller } from '../../controllers/deviceController.js';
import { DeviceService } from '../../services/deviceService.js';
import { DataService } from '../../services/dataService.js';
import { TimeService } from '../../services/timeService.js';
import queryLogs from '../../utils/queryLogs.js';

export default async function deviceRoutes(fastify, options) {

    const knex = fastify.knex;

    const deviceService = new DeviceService(knex);
    const dataService = new DataService(knex, deviceService, fastify.log);
    const timeService = new TimeService();

    const deviceController = new DeviceController(
        deviceService,
        dataService,
        timeService
    );

    // sending data (metadata or device data)
    fastify.post('/data', {
        handler: (request, reply) => {
            if (request.headers['dev-time']) {
                return deviceController.handleDevicePost(request, reply);
            } else {
                return deviceController.handleHandshake(request, reply);
            }
        }
    });

    // fetching metadata history>>> tested
    fastify.get('/meta-history/:mac', {
        handler: (request, reply) => deviceController.getMetadataHistory(request, reply)
    });
    
    // Query logs endpoint
    fastify.get('/logs',queryLogs);
}
