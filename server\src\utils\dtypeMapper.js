export class DtypeMapper {
    static getSQLiteType(dtype) {
        const typeMap = {
            'INT': 'INTEGER',
            'FLOAT': 'REAL',
            'STR': 'TEXT',
            'BOOL': 'INTEGER', // sqlite doesn't have native boolean
            'TIMESTAMP': 'INTEGER' // store as unix timestamp
        };
        return typeMap[dtype] || 'TEXT';
    }

    static formatValueForSQL(value, dtype) {
        if (value === null) return null;

        switch (dtype) {
            case 'INT':
                return parseInt(value);
            case 'FLOAT':
                return parseFloat(value);
            case 'BOOL':
                return value ? 1 : 0;
            case 'TIMESTAMP':
                return typeof value === 'string' ? Date.parse(value) / 1000 : value;
            case 'STR':
                return String(value);
            default:
                return value;
        }
    }
}