/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function up(knex) {
    return knex.schema.createTable('devices', table => {
        table.integer('macid').primary()                    // primary key - mac address (integer format) 
        table.string('mac').unique().notNullable()          // actual mac address to device (string format)
        table.string('model')
        table.string('label')
        table.string('description')
        table.integer('oid').unsigned().notNullable().references('oid').inTable('organizations').onDelete('CASCADE')
        table.string('apikey')
        table.string('remip')
        table.integer('stime')
        table.integer('rtime')
        table.integer('utime')
        table.integer('ctime')
        table.integer('dprotect')
        table.integer('nchan').notNullable()
        table.integer('rewind')
        table.string('error')
        table.float('comment')
        table.float('longitude')
        table.float('latitude')
        table.string('candidate')
        table.string('object')
        table.string('data')
    })
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function down(knex) {
    return knex.schema.dropTable('devices');
}