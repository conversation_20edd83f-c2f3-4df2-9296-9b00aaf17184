// // const macaddr = '00:1A:2B:3C:4D:5E'
// // const macid = parseInt(macaddr.replace(/:/g, ''), 16)

// // console.log(macid)

// import { dirname, join } from 'path';
// import { fileURLToPath } from 'url';

// const __dirname = dirname(fileURLToPath(import.meta.url));
// console.log(__dirname)
// const mod_dir =join(__dirname, 'src/db/migrations')
// console.log(mod_dir)

// const mac = "BC:24:11:84:4B:EF";
// const cleanMac = mac.replace(/:/g, '').toLowerCase();
// const macidBigInt = BigInt(`0x${cleanMac}`);
// console.log(macidBigInt.toString().length);
// const macid = BigInt(206863098727407); 
// console.log(macid===macidBigInt);

// // Use BigInt for large hex values to avoid overflow
// const macid = Number(BigInt(`0x${cleanMac}`));
// console.log(`Converted MAC to decimal: ${macid}`);

// // Verify the conversion
// const hexCheck = macid.toString(16).padStart(12, '0');
// console.log(`Verification - decimal back to hex: ${hexCheck}`);
// console.log(`Original clean MAC: ${cleanMac}`);
// console.log(`Conversion match: ${hexCheck === cleanMac ? 'Yes' : 'No'}`);

// 112394521950
// 9223372036854775807
// const ass = '9223372036854775807'
// console.log(ass.length)
// console.log(assg.registers.length);

const docnum = '1657582123'
const curnum = Date.now()

console.log(curnum)
console.log(docnum)
