import { config } from 'dotenv';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __dirname = dirname(fileURLToPath(import.meta.url));

config({
    path: `.env.${process.env.NODE_ENV || 'development'}`
});

// Ensure data directory exists
const dataDir = join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

const baseConfig = {
    client: 'sqlite3',
    useNullAsDefault: true,
    migrations: {
        directory: join(__dirname, '/migrations')
    },
    pool: {
        afterCreate: (conn, cb) => {
            conn.run('PRAGMA foreign_keys = ON', cb);
        }
    }
};

export default {
    development: {
        ...baseConfig,
        connection: {
            filename: join(dataDir, 'App.db')
        }
    },
    production: {
        ...baseConfig,
        connection: {
            filename: join(dataDir, 'App.db')
        }
    }
};
