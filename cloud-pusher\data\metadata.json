{"macid": 92312076291, "mac": "00:15:7E:3A:80:03", "model": "GeoSCADA-NR1", "label": "Nordic B", "description": "500 Wilson Ave., Toronto", "registers": [{"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "TS01", "units": "°C", "display": 2}, {"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "TS02", "units": "°C", "display": 2}, {"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "TS03", "units": "°C", "display": 2}, {"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "TS04", "units": "°C", "display": 2}, {"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "TS00", "units": "°C", "display": 2}, {"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "PS01", "units": "PSIG", "display": 2}, {"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "PS02", "units": "PSIG", "display": 2}, {"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "PS03", "units": "PSIG", "display": 2}, {"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "PS04", "units": "PSIG", "display": 2}, {"dtype": "F32", "group": "AnalogIn", "device": "AnalogIn", "register": "FS01", "units": "US GPM", "display": 2}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "ActualSpeedRPM", "units": "RPM"}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "MotorPower", "units": "kW", "display": 3}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "SensorlessHead", "units": "ft", "display": 2}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "Status", "units": null}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "HOAState", "units": null}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "TripPumpRunningHours", "units": "h"}, {"dtype": "F32", "group": "Pump1", "device": "Pump1", "register": "TripPumpRunningkWhCounter", "units": "kWh"}, {}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "ActualSpeedRPM", "units": "RPM"}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "MotorPower", "units": "kW", "display": 3}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "MotorInputVoltage", "units": "V", "display": 1}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "MotorInputCurrent", "units": "A", "display": 2}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "SensorlessHead", "units": "ft", "display": 2}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "SensorlessFlow", "units": "US GPM", "display": 2}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "Status", "units": null}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "ControlSetpoint", "units": "US GPM", "display": 2}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "HOAState", "units": null}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "TripPumpRunningHours", "units": "h"}, {"dtype": "F32", "group": "Pump2", "device": "Pump2", "register": "TripPumpRunningkWhCounter", "units": "kWh"}, {}, {"dtype": "B", "group": "virtual", "device": "virtual", "register": "PurgeIpr"}, {"dtype": "F64", "group": "virtual", "device": "virtual", "register": "HeatTransferIn", "units": "BTU", "display": 2}, {"dtype": "F64", "group": "virtual", "device": "virtual", "register": "HeatTransferInFS01", "units": "BTU", "display": 2}, {"dtype": "F64", "group": "virtual", "device": "virtual", "register": "HeatTransferOut", "units": "BTU", "display": 2}, {"dtype": "F64", "group": "virtual", "device": "virtual", "register": "HeatTransferOutFS01", "units": "BTU", "display": 2}, {"dtype": "F64", "group": "virtual", "device": "virtual", "register": "HeatTransferRate", "units": "BTU/h", "display": 2}, {"dtype": "F64", "group": "virtual", "device": "virtual", "register": "HeatTransferRateFS01", "units": "BTU/h", "display": 2}, {"dtype": "F32", "group": "bas", "device": "bas", "register": "HeatTransferRateBAS", "units": "BTU/h"}, {"dtype": "F32", "group": "bas", "device": "bas", "register": "HeatTransferBAS", "units": "BTU"}, {}, {}, {"dtype": "STR", "group": "virtual", "device": "virtual", "register": "PumpState"}, {"dtype": "B", "group": "virtual", "device": "virtual", "register": "PumpError1"}, {"dtype": "B", "group": "virtual", "device": "virtual", "register": "PumpError2"}, {"dtype": "B", "group": "virtual", "device": "virtual", "register": "AnalogInError1"}, {"dtype": "B", "group": "virtual", "device": "virtual", "register": "AnalogInError2"}]}