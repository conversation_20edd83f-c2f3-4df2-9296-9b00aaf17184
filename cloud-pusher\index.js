import fs from 'fs';
import path from 'path';
import fetch from 'node-fetch';
import DEFAULT_CONFIG from './config/defaultconfig.js';
import config from './config/config.js';
import { STATES, state } from './state.js';
import sleep from './helpers/sleep.js';
import { loadPersistedFailedBatches, persistFailedBatches, handleFailedBatch } from './helpers/persistFailed.js';
import performHandshake from './network/handshake.js';
import parseCsvLineToTypedValues from './helpers/dataParsing.js';
import { sendDataWithRetries, processPersistedFailedBatchesRetry } from './network/sendData.js';
import runLiveIteration from './modes/live.js';
import runCatchupIteration from './modes/catchup.js';
import { getRegisterValidationInfo, liveDataGenerators } from './modes/liveDataGen.js';
import { processBatchQueue, getBatchQueueStats } from './modes/batchProcessor.js';
import generateLiveDataForUnified from './modes/unifiedLive.js';
import processCsvDataForUnified from './modes/unifiedCatchup.js';

// --- Command Line Arguments ---
function showHelp() {
    console.log(`

Usage: node index.js [options]

Options:
  --help                       Show this help message
  --server <url>               Server base URL (default: ${DEFAULT_CONFIG.server.baseUrl})
  --data-dir <path>            Data directory (default: ${DEFAULT_CONFIG.dataDir})
  --batch-size <number>        Batch size (default: ${DEFAULT_CONFIG.batchSize})
  --interval <number>          Interval between sending batches in ms (default: ${DEFAULT_CONFIG.interval})
  --format <format>            Data format: json-array or csv (default: ${DEFAULT_CONFIG.format})
  --network-timeout <ms>       Network timeout for API calls (default: ${DEFAULT_CONFIG.networkTimeout})
  --failed-buffer-limit <num>  In-memory limit for failed batches (default: ${DEFAULT_CONFIG.failedBatchBufferLimit})
  --failed-batches-path <path> File path for persisted failed batches (default: ${DEFAULT_CONFIG.failedBatchesFilePath})
  --mode <mode>                Operation mode: catchup, live, both, or unified (default: ${DEFAULT_CONFIG.mode})
  --live-interval <ms>         Interval for generating live data batches (default: ${DEFAULT_CONFIG.liveMode.interval})
  --live-source <source>       Live data source: simulator or device (default: ${DEFAULT_CONFIG.liveMode.source})
  --live-batch-size <number>   Number of data points per batch in live mode (default: ${DEFAULT_CONFIG.liveMode.batchSize})
  --service-interval <ms>      Main service tick interval in ms (default: ${DEFAULT_CONFIG.serviceIntervalMs})
  --unified-batch-interval <ms> Interval for sending unified batches (default: ${DEFAULT_CONFIG.unifiedMode.batchSendInterval})
  --unified-max-batch <number> Maximum data points per unified batch (default: ${DEFAULT_CONFIG.unifiedMode.maxBatchSize})
  --csv-processing-rate <num>  Lines to process per tick from CSV files in unified mode (default: ${DEFAULT_CONFIG.unifiedMode.csvProcessingRate})
  `);
}

function parseArgs(processArgs) {
    const parsed = {};
    for (let i = 0; i < processArgs.length; i++) {
        const arg = processArgs[i];
        if (arg.startsWith('--')) {
            const key = arg.slice(2);
            const nextArg = processArgs[i + 1];
            const value = (nextArg && !nextArg.startsWith('--')) ? nextArg : true;

            if (key === 'help') {
                showHelp();
                process.exit(0);
            }
            parsed[key] = value;
            if (value !== true) i++;
        }
    }
    return parsed;
}

function applyArgsToConfig(args) {
    if (args.server) config.server.baseUrl = args.server;
    if (args['data-dir']) config.dataDir = args['data-dir'];
    if (args['batch-size']) config.batchSize = parseInt(args['batch-size'], 10);
    if (args.interval) config.interval = parseInt(args.interval, 10);
    if (args.format && ['json-array', 'csv'].includes(args.format)) config.format = args.format;
    if (args['network-timeout']) config.networkTimeout = parseInt(args['network-timeout'], 10);
    if (args['failed-buffer-limit']) config.failedBatchBufferLimit = parseInt(args['failed-buffer-limit'], 10);
    if (args['failed-batches-path']) config.failedBatchesFilePath = args['failed-batches-path'];
    if (args.mode && ['catchup', 'live', 'both', 'unified'].includes(args.mode)) {
        config.mode = args.mode;
    }
    if (args['live-interval']) {
        config.liveMode.interval = parseInt(args['live-interval'], 10);
    }
    if (args['live-source'] && ['simulator', 'device'].includes(args['live-source'])) {
        config.liveMode.source = args['live-source'];
    }
    if (args['service-interval']) {
        config.serviceIntervalMs = parseInt(args['service-interval'], 10);
    }
    if (args['live-batch-size']) {
        config.liveMode.batchSize = parseInt(args['live-batch-size'], 10);
    }
    if (args['unified-batch-interval']) {
        config.unifiedMode.batchSendInterval = parseInt(args['unified-batch-interval'], 10);
    }
    if (args['unified-max-batch']) {
        config.unifiedMode.maxBatchSize = parseInt(args['unified-max-batch'], 10);
    }
    if (args['csv-processing-rate']) {
        config.unifiedMode.csvProcessingRate = parseInt(args['csv-processing-rate'], 10);
    }
}

// --- State Machine ---

async function performInitLogic() {
    console.log("State: INITIALIZING");
    state.currentGlobalState = STATES.INITIALIZING;

    const handshakeResult = await performHandshake();
    if (!handshakeResult.success) {
        console.error("Initialization failed during handshake.");
        state.currentGlobalState = STATES.HANDSHAKE_FAILED;
        if (state.inMemoryFailedBatches.length > 0) persistFailedBatches();
        return;
    }

    state.initData = handshakeResult; // { metadata, serverTime, latestTimestampFromServer }
    state.lastSentTimestamp = state.initData.latestTimestampFromServer;
    state.catchupProgress.lastCsvTimestampSent = state.initData.latestTimestampFromServer;
    console.log(`Initial lastSentTimestamp set from server: ${state.lastSentTimestamp}`);

    // process batches that failed in previous runs
    loadPersistedFailedBatches();
    if (state.inMemoryFailedBatches.length > 0) {
        await processPersistedFailedBatchesRetry();
    }

    if (config.mode === 'unified') {
        // Initialize both catchup and live for unified mode
        state.catchupProgress.fileQueue = [];
        ['fine','coarse'].forEach(dirType => {
            const dirPath = path.join(config.dataDir, dirType);
            if (fs.existsSync(dirPath)) {
                const files = fs.readdirSync(dirPath)
                    .filter(file => file.endsWith('.csv'))
                    .sort()
                    .map(file => ({ type: dirType, path: path.join(dirPath, file), name: file }));
                state.catchupProgress.fileQueue.push(...files);
            }
        });

        state.catchupProgress.currentFileIndex = 0;
        state.catchupProgress.currentLineInFile = 0;
        state.catchupProgress.allCatchupDataSent = (state.catchupProgress.fileQueue.length === 0);

        // Initialize live mode runtime state for unified mode
        state.liveModeRuntimeState.registerValidation = await getRegisterValidationInfo(state.initData.metadata);
        state.liveModeRuntimeState.lastValues = [null, ...state.initData.metadata.registers.map((_reg, idx) =>
            state.liveModeRuntimeState.registerValidation[idx]?.defaultValue
        )];

        console.log(`Transitioning to UNIFIED mode. ${state.catchupProgress.fileQueue.length} CSV files to process + live data generation.`);
        state.currentGlobalState = STATES.UNIFIED;
        return;
    }

    if (config.mode === 'catchup' || config.mode === 'both') {
        state.catchupProgress.fileQueue = [];
        ['fine','coarse'].forEach(dirType => {
            const dirPath = path.join(config.dataDir, dirType);
            if (fs.existsSync(dirPath)) {
                const files = fs.readdirSync(dirPath)
                    .filter(file => file.endsWith('.csv'))
                    .sort()
                    .map(file => ({ type: dirType, path: path.join(dirPath, file), name: file }));
                state.catchupProgress.fileQueue.push(...files);
            }
        });

        state.catchupProgress.currentFileIndex = 0;
        state.catchupProgress.currentLineInFile = 0;
        state.catchupProgress.allCatchupDataSent = (state.catchupProgress.fileQueue.length === 0);

        if (!state.catchupProgress.allCatchupDataSent) {
            console.log(`Prepared for CATCHUP mode. ${state.catchupProgress.fileQueue.length} files to process.`);
            state.currentGlobalState = STATES.CATCHUP;
            return;
        } else {
            console.log("No historical data files found for CATCHUP mode or all processed.");
        }
    }

    if (config.mode === 'live' || config.mode === 'both') {
        console.log("Transitioning to LIVE mode post-init.");
        state.liveModeRuntimeState.registerValidation = await getRegisterValidationInfo(state.initData.metadata);
        state.liveModeRuntimeState.lastValues = [null, ...state.initData.metadata.registers.map((_reg, idx) =>
            state.liveModeRuntimeState.registerValidation[idx]?.defaultValue
        )];
        state.currentGlobalState = STATES.LIVE;
    } else {
        console.log("Cloud Pusher initialization complete. No further modes configured (catchup done/skipped, live not enabled).");
        state.currentGlobalState = STATES.IDLE; // Or STOPPED if it should terminate
        if (state.tickServiceCancel) state.tickServiceCancel();
    }
}

/**
 * Starts an interval that aligns execution as close as possible to the start of each specified interval.
 */
function startAlignedInterval(callback, interval = 1000) {
    let timeoutId;
    let running = true;

    function scheduleNextTick() {
        if (!running) return;
        const now = Date.now();
        const delay = interval - (now % interval);

        timeoutId = setTimeout(async () => {
            if (!running) return;
            await callback();
            if (running) scheduleNextTick();
        }, delay);
    }

    scheduleNextTick();

    return () => {
        console.log("Cancelling aligned interval.");
        running = false;
        clearTimeout(timeoutId);
    };
}

// Main service tick
async function tickService() {
    if (state.isTickServiceRunning) {
        console.log("Tick service already running, skipping this tick.");
        return;
    }

    state.isTickServiceRunning = true;

    try {
        switch (state.currentGlobalState) {
            case STATES.IDLE:
                // If idle but should be running based on config, try to initialize.
                if (config.mode === 'catchup' || config.mode === 'live' || config.mode === 'both' || config.mode === 'unified') {
                    console.log("State: IDLE, attempting to initialize...");
                    await performInitLogic();
                } else {
                    console.log("State: IDLE. No active modes configured. Stopping.");
                    if (state.tickServiceCancel) state.tickServiceCancel();
                    state.currentGlobalState = STATES.STOPPED;
                }
                break;

            case STATES.INITIALIZING:
                console.log("State: INITIALIZING (waiting for completion or recovery)...");
                break;

            case STATES.HANDSHAKE_FAILED:
                console.log("State: HANDSHAKE_FAILED. Attempting re-initialization...");
                await sleep(config.retryOptions.retryDelay * 2);
                await performInitLogic();
                break;

            case STATES.CATCHUP:
                if (!state.initData) {
                    console.error("CATCHUP: Missing initData. Reverting to HANDSHAKE_FAILED.");
                    state.currentGlobalState = STATES.HANDSHAKE_FAILED;
                    break;
                }
                // console.log("State: CATCHUP - Running catchup iteration.");
                const catchupResult = await runCatchupIteration();
                if (catchupResult.isComplete) {
                    console.log("Catchup mode completed.");
                    persistFailedBatches();

                    if (config.mode === 'live' || config.mode === 'both') {
                        console.log("Transitioning to LIVE mode.");
                        state.liveModeRuntimeState.registerValidation = await getRegisterValidationInfo(state.initData.metadata);
                        state.liveModeRuntimeState.lastValues = [null, ...state.initData.metadata.registers.map((_reg, idx) =>
                            state.liveModeRuntimeState.registerValidation[idx]?.defaultValue
                        )];
                        state.liveModeRuntimeState.lastBatchSentTime = null;
                        state.currentGlobalState = STATES.LIVE;
                    } else {
                        console.log("Cloud Pusher catchup finished. Live mode not configured. Stopping.");
                        state.currentGlobalState = STATES.STOPPED;
                        if (state.tickServiceCancel) state.tickServiceCancel();
                    }
                }
                break;

            case STATES.LIVE:
                if (!state.initData) {
                    console.error("LIVE: Missing initData. Reverting to HANDSHAKE_FAILED.");
                    state.currentGlobalState = STATES.HANDSHAKE_FAILED;
                    break;
                }
                // console.log("State: LIVE - Running live iteration.");
                await runLiveIteration();
                break;

            case STATES.UNIFIED:
                if (!state.initData) {
                    console.error("UNIFIED: Missing initData. Reverting to HANDSHAKE_FAILED.");
                    state.currentGlobalState = STATES.HANDSHAKE_FAILED;
                    break;
                }

                // Generate live data and add to batch queue
                await generateLiveDataForUnified();

                // Process CSV data and add to batch queue (if not all processed)
                if (!state.catchupProgress.allCatchupDataSent) {
                    const csvResult = await processCsvDataForUnified();
                    if (csvResult.isComplete) {
                        console.log("UNIFIED: All CSV data has been processed. Continuing with live data only.");
                    }
                }

                // Process and send batches from the unified queue
                await processBatchQueue();

                // Log statistics periodically
                const stats = getBatchQueueStats();
                if (stats.totalBatchesSent % 10 === 0 && stats.totalBatchesSent > 0) {
                    console.log(`UNIFIED: Stats - Queue: ${stats.queueSize}, Batches sent: ${stats.totalBatchesSent}, Live: ${stats.liveDataCount}, CSV: ${stats.csvDataCount}`);
                }
                break;

            case STATES.ERROR:
                console.error("State: ERROR. encountered a critical error. Stopping.");
                if (state.tickServiceCancel) state.tickServiceCancel();
                persistFailedBatches();
                state.currentGlobalState = STATES.STOPPED;
                break;

            case STATES.STOPPED:
                console.log("State: STOPPED. Cloud Pusher is not running.");
                // The interval should have been cancelled already.
                break;

            default:
                console.warn(`Unknown state: ${state.currentGlobalState}. Resetting to IDLE.`);
                state.currentGlobalState = STATES.IDLE;
                break;
        }
    } catch (error) {
        console.error("Critical error in tickService:", error.message, error.stack);
        state.currentGlobalState = STATES.ERROR; // Move to error state
    } finally {
        state.isTickServiceRunning = false;
    }
}

// --- Main ---
async function runCloudPusher() {
    const cliArgs = parseArgs(process.argv.slice(2));
    applyArgsToConfig(cliArgs);

    console.log('Initializing Cloud Pusher with configuration:', config);
    state.currentGlobalState = STATES.IDLE; // Initial state

    // Start the main service loop
    console.log(`Starting main service loop with interval: ${config.serviceIntervalMs}ms`);
    state.tickServiceCancel = startAlignedInterval(tickService, config.serviceIntervalMs);

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
        console.log("\nSIGINT received. Shutting down Cloud Pusher...");
        if (state.tickServiceCancel) {
            state.tickServiceCancel(); // stopping aligned interval
        }
        state.currentGlobalState = STATES.STOPPED;
        await sleep(config.serviceIntervalMs + 500); // edge case : current tick to finish
        console.log("Persisting failed batches before exit...");
        persistFailedBatches();
        console.log("...Graceful shutdown complete...");
        process.exit(0);
    });
}

runCloudPusher().catch(error => {
    console.error("Unhandled critical error :", error.message, error.stack);
    state.currentGlobalState = STATES.ERROR;
    if (state.inMemoryFailedBatches && typeof persistFailedBatches === 'function') {
        console.log("Attempting to persist failed batches due to unhandled top-level error...");
        persistFailedBatches();
    }
    process.exit(1);
});
