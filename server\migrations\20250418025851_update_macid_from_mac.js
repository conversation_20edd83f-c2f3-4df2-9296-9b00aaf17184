/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function up(knex) {
    await knex.raw(`
        CREATE TRIGGER set_macid_from_mac
        AFTER INSERT ON devices
        FOR EACH ROW
        BEGIN
            UPDATE devices
            SET macid = CAST(HEX(LOWER(REPLACE(NEW.mac, ':', ''))) AS INTEGER)
            WHERE rowid = NEW.rowid;
        END;
        `);
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function down(knex) {
    await knex.raw('DROP TRIGGER IF EXISTS set_macid_from_mac;');
}