import { state, STATES } from './state.js';
import { addDataToBatch, processBatchQueue } from './modes/batchProcessor.js';
import processCsvDataForUnified from './modes/unifiedCatchup.js';
import generateLiveDataForUnified from './modes/unifiedLive.js';
import config from './config/config.js';

// Mock initialization data for testing
const mockInitData = {
    metadata: {
        mac: '00:15:7E:3A:80:03',
        registers: [
            { name: 'temp1', dtype: 'f64' },
            { name: 'temp2', dtype: 'f64' },
            { name: 'temp3', dtype: 'f64' }
        ]
    },
    serverTime: Date.now(),
    latestTimestampFromServer: 0 // Start from 0 like in your logs
};

// Test function to debug unified mode
async function testUnifiedModeDebug() {
    console.log('=== Testing Unified Mode Debug ===\n');
    
    // Initialize state exactly like in your setup
    state.initData = mockInitData;
    state.lastSentTimestamp = mockInitData.latestTimestampFromServer;
    state.catchupProgress.lastCsvTimestampSent = mockInitData.latestTimestampFromServer;
    state.currentGlobalState = STATES.UNIFIED;
    
    // Set up CSV file queue like the real system
    state.catchupProgress.fileQueue = [
        { type: 'fine', path: './data/fine/D19825.csv', name: 'D19825.csv' }
    ];
    state.catchupProgress.currentFileIndex = 0;
    state.catchupProgress.currentLineInFile = 0;
    state.catchupProgress.allCatchupDataSent = false;
    
    console.log(`Initial state:`);
    console.log(`  lastSentTimestamp: ${state.lastSentTimestamp}`);
    console.log(`  lastCsvTimestampSent: ${state.catchupProgress.lastCsvTimestampSent}`);
    console.log(`  CSV files to process: ${state.catchupProgress.fileQueue.length}\n`);
    
    // Mock the sendDataWithRetries function to see what data is being sent
    const sendDataModule = await import('./network/sendData.js');
    const originalSendData = sendDataModule.sendDataWithRetries;
    
    sendDataModule.sendDataWithRetries = async (data) => {
        console.log(`\n=== MOCK SEND DATA ===`);
        console.log(`Sending ${data.length} data points to server:`);
        
        // Group by timestamp ranges to see CSV vs live data
        const csvData = data.filter(point => point[0] < 1700000000); // Before 2023
        const liveData = data.filter(point => point[0] >= 1700000000); // After 2023
        
        console.log(`  CSV data points: ${csvData.length}`);
        if (csvData.length > 0) {
            console.log(`    First CSV: timestamp=${csvData[0][0]}, values=[${csvData[0].slice(1, 4).join(', ')}]`);
            console.log(`    Last CSV: timestamp=${csvData[csvData.length-1][0]}, values=[${csvData[csvData.length-1].slice(1, 4).join(', ')}]`);
        }
        
        console.log(`  Live data points: ${liveData.length}`);
        if (liveData.length > 0) {
            console.log(`    First Live: timestamp=${liveData[0][0]}, values=[${liveData[0].slice(1, 4).join(', ')}]`);
            console.log(`    Last Live: timestamp=${liveData[liveData.length-1][0]}, values=[${liveData[liveData.length-1].slice(1, 4).join(', ')}]`);
        }
        
        console.log(`=== END MOCK SEND ===\n`);
        return { success: true, status: 200, data: 'OK' };
    };
    
    try {
        console.log('1. Processing CSV data...');
        const csvResult = await processCsvDataForUnified();
        console.log('CSV processing result:', csvResult);
        
        console.log('\n2. Generating live data...');
        const liveResult = await generateLiveDataForUnified();
        console.log('Live generation result:', liveResult);
        
        console.log('\n3. Processing batch queue...');
        // Force batch processing
        state.unifiedBatchState.lastBatchSentTime = 0;
        const batchResult = await processBatchQueue();
        console.log('Batch processing result:', batchResult);
        
        console.log('\n4. Final state:');
        console.log(`  lastSentTimestamp: ${state.lastSentTimestamp}`);
        console.log(`  lastCsvTimestampSent: ${state.catchupProgress.lastCsvTimestampSent}`);
        console.log(`  Queue size: ${state.unifiedBatchState.pendingDataQueue.length}`);
        
    } catch (error) {
        console.error('Error during testing:', error);
    } finally {
        // Restore original function
        sendDataModule.sendDataWithRetries = originalSendData;
    }
    
    console.log('\n=== Unified Mode Debug Test Complete ===');
}

// Run the test if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    testUnifiedModeDebug().catch(console.error);
}

export { testUnifiedModeDebug };
