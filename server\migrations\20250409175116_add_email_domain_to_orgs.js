/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function up(knex) {
    return knex.schema.table('organizations', table => {
        table.string('email_domain').nullable().comment('Domain used for auto-assigning users to this organization');
        table.index('email_domain');
    });
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function down(knex) {
    return knex.schema.table('organizations', table => {
        table.dropIndex('email_domain');
        table.dropColumn('email_domain');
    });
}
