/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function up(knex) {
    return knex.schema.table('devices', table => {
        table.json('meta').nullable().comment('Device metadata in JSON format for compatibility');
    });
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function down(knex) {
    return knex.schema.table('devices', table => {
        table.dropColumn('meta');
    });
}
