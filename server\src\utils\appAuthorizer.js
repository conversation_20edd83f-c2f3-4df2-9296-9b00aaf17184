import knex from 'knex'

const ROLES = {
    DISABLED: [0, 0],        // [VALUE, THRESHOLD]
    USER: [3, 1],            // role >= 1
    AUTHOR: [15, 8],         // role >= 8
    ORGADMIN: [255, 64]      // role >= 64
}


async function authorizer(knex, payload, action, resource_type, resource_id ) {
    const requester_uid = payload.uid 
    try {
        // // checks >>>>>
        // 1:  superuser check 
        if (payload.admin === 1) {
            return 1 // superuser has full access
        }


        // 2: non-superuser authorization rules
        if (resource_type === 'user') {
            if (action === 'read') {
                if (requester_uid === resource_id) {
                    return 1 // read self
                }
                //  if they share at least one organization
                const sharedOrg = await knex('users_organizations as uo1')
                    .join('users_organizations as uo2', 'uo1.oid', 'uo2.oid')
                    .where('uo1.uid', requester_uid)
                    .andWhere('uo2.uid', resource_id)
                    .select('uo1.oid')
                    .first()
                // return sharedOrg ? 1 : 0
                if (!sharedOrg) {
                    const userExists = await knex('users')
                        .where('uid', resource_id)
                        .select(1)
                        .first()
                    return userExists ? 0 : -1
                }
                return 1
            } else if (action === 'modify') {
                if (requester_uid === resource_id) {
                    return 1 // can modify self
                }
                //  if requester is orgadmin in an organization where target user is a member
                const authOrg = await knex('users_organizations as uo1')
                    .join('users_organizations as uo2', 'uo1.oid', 'uo2.oid')
                    .where('uo1.uid', requester_uid)
                    .andWhere('uo1.role', '>=', ROLES.ORGADMIN[1]) // orgadmin threshold
                    .andWhere('uo2.uid', resource_id)
                    .select('uo1.oid')
                    .first()

                if (!authOrg) {
                    const userExists = await knex('users')
                        .where('uid', resource_id)
                        .select(1)
                        .first()
                    return userExists ? 0 : -1
                }
                return 1
            }
        } else if (resource_type === 'organization') {
            if (action === 'read') {
                const membership = await knex('users_organizations as uo')
                    .join('organizations as o', 'o.oid', 'uo.oid') 
                    .where('uo.uid', requester_uid)
                    .andWhere('uo.oid', resource_id)
                    .andWhere('uo.role', '>=', ROLES.USER[1])
                    .select('uo.oid')
                    .first()
                if (!membership) {
                    const orgExists = await knex('organizations')
                        .where('oid', resource_id)
                        .select(1)
                        .first()
                    return orgExists ? 0 : -1
                }
                return 1 
            } else if (action === 'modify') {
                const orgadmin = await knex('users_organizations as uo')
                    .join('organizations as o', 'o.oid', 'uo.oid')
                    .where('uo.uid', requester_uid)
                    .andWhere('uo.oid', resource_id)
                    .andWhere('uo.role', '>=', ROLES.ORGADMIN[1])
                    .select('uo.oid')
                    .first()
                if (!orgadmin) {
                    const orgExists = await knex('organizations')
                        .where('oid', resource_id)
                        .select(1)
                        .first()
                    return orgExists ? 0 : -1
                }
                return 1
            }
        } else if (resource_type === 'device') {
            const device = await knex('devices')
                .where('macid', resource_id)
                .select('oid')
                .first()
            if (!device) {
                return -1
            }
            const device_oid = device.oid
            if (action === 'read') {
                const access = await knex('users_organizations')
                    .where('uid', requester_uid)
                    .andWhere('oid', device_oid)
                    .andWhere('role', '>=', ROLES.USER[1])
                    .select(1)
                    .first()
                return access ? 1 : 0
            } else if (action === 'modify') {
                const access = await knex('users_organizations')
                    .where('uid', requester_uid)
                    .andWhere('oid', device_oid)
                    .andWhere('role', '>=', ROLES.ORGADMIN[1])
                    .select(1)
                    .first()
                return access ? 1 : 0
            }
        }

        // default to not authorized
        return 0
    } catch (error) {
        console.error('Authorization error:', error);
        return 0; // default to not authorized on error
    }
}

export default authorizer


