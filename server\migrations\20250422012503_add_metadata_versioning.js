/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function up(knex) {
    return knex.schema.alterTable('devices', table => {
        table.integer('meta_version').defaultTo(1);
        table.json('meta_history');
    });
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function down(knex) {
    return knex.schema.alterTable('devices', table => {
        table.dropColumn('meta_version');
        table.dropColumn('meta_history');
    });
}


