// purpose: handling many to many relationship between users and organizations
/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function up(knex) {
    return knex.schema.createTable('users_organizations', table => {
        table.integer('uid').unsigned().notNullable().references('uid').inTable('users').onDelete('CASCADE')
        table.integer('oid').unsigned().notNullable().references('oid').inTable('organizations').onDelete('CASCADE')
        table.integer('role').notNullable().defaultTo(0)
                    // 0 - disabled - 00-00-00-00
                    // 3 - user - 00-00-00-11
                    // 15 - author - 00-00-11-11
                    // 255 - admin - 11-11-11-11
        table.primary(['uid', 'oid']) 
    })
}

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
export async function down(knex) {
    return knex.schema.dropTable('users_organizations')
}