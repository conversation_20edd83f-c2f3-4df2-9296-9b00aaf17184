export const roleMap = [
    { role: 'OrgAdmin', value: 255, threshold: 64 },
    { role: 'Author', value: 15, threshold: 8 },
    { role: 'User', value: 3, threshold: 1 },
    { role: 'Disabled', value: 0, threshold: 0 }
];

/**
 * @param {number} roleValue - role value to decode.
 * @returns {string} name of the role corresponding to role value.
 */
export function getRoleName(roleValue) {
    for (const { role, threshold } of roleMap) {
        if (roleValue >= threshold) {
            return role;
        }
    }
    return 'Unknown';       // hope we never get here
}