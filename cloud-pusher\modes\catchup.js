import fs from 'fs';
import config from '../config/config.js';
import { sendDataWithRetries } from '../network/sendData.js';
import { handleFailedBatch } from '../helpers/persistFailed.js';
import parseCsvLineToTypedValues from '../helpers/dataParsing.js';
import { STATES, state } from '../state.js';

async function runCatchupIteration() {
    if (!state.initData) {
        console.error("CATCHUP: Cannot run catchup mode without initialization data.");
        state.currentGlobalState = STATES.HANDSHAKE_FAILED;
        return { isComplete: false, newTimestamp: state.lastSentTimestamp };
    }

    if (state.catchupProgress.currentFileIndex >= state.catchupProgress.fileQueue.length) {
        console.log("All catchup files processed.");
        state.catchupProgress.allCatchupDataSent = true;
        return { isComplete: true, newTimestamp: state.lastSentTimestamp };
    }

    const currentFile = state.catchupProgress.fileQueue[state.catchupProgress.currentFileIndex];
    console.log(`CATCHUP: Processing file ${currentFile.name} (index ${state.catchupProgress.currentFileIndex}), starting line ${state.catchupProgress.currentLineInFile}`);

    let fileContent;
    try {
        fileContent = fs.readFileSync(currentFile.path, 'utf8');
    } catch (e) {
        console.error(`Error reading catchup file ${currentFile.path}: ${e.message}`);
        state.catchupProgress.currentFileIndex++;
        state.catchupProgress.currentLineInFile = 0;
        return { isComplete: false, newTimestamp: state.lastSentTimestamp }; 
    }

    const lines = fileContent.trim().split('\n');
    const batchForSending = [];
    let linesProcessedInThisCall = 0;
    let latestTimestampInBatch = state.lastSentTimestamp;

    for (let i = state.catchupProgress.currentLineInFile; i < lines.length; i++) {
        const line = lines[i];
        linesProcessedInThisCall++;
        if (!line.trim()) continue;

        const typedValues = parseCsvLineToTypedValues(line, state.initData.metadata);
        if (!typedValues) {
            console.warn(`Skipping invalid CSV line in ${currentFile.name}: ${line}`);
            continue;
        }
        const timestamp = typedValues[0];

        if (timestamp > state.lastSentTimestamp) { 
            batchForSending.push(typedValues);
            latestTimestampInBatch = Math.max(latestTimestampInBatch, timestamp);
        }

        if (batchForSending.length >= config.batchSize) {
            break; // Found a full batch
        }
    }

    // Update line pointer for the next iteration on file
    state.catchupProgress.currentLineInFile += linesProcessedInThisCall;

    if (batchForSending.length > 0) {
        console.log(`CATCHUP: Sending batch of ${batchForSending.length} records from ${currentFile.name}. Newest timestamp in batch: ${latestTimestampInBatch}`);
        
        // Create a proper data object with MAC address
        const dataWithMac = {
            mac: state.initData.metadata.mac,
            data: batchForSending
        };
        
        const sendResult = await sendDataWithRetries(dataWithMac);
        
        if (sendResult.success) {
            state.lastSentTimestamp = latestTimestampInBatch; 
            console.log(`CATCHUP: Batch from ${currentFile.name} sent. New lastSentTimestamp: ${state.lastSentTimestamp}`);
        } else {
            console.error(`CATCHUP: Failed to send batch from ${currentFile.name}. Adding to failed buffer.`);
            handleFailedBatch(dataWithMac, state.initData.metadata.mac);
            return { isComplete: false, newTimestamp: state.lastSentTimestamp };
        }
    } else {
        console.log(`CATCHUP: No new data in processed lines of ${currentFile.name} (up to line ${state.catchupProgress.currentLineInFile}).`);
    }

    // If all lines in current file processed
    if (state.catchupProgress.currentLineInFile >= lines.length) {
        console.log(`CATCHUP: Finished processing file ${currentFile.name}.`);
        state.catchupProgress.currentFileIndex++;
        state.catchupProgress.currentLineInFile = 0;
        if (state.catchupProgress.currentFileIndex >= state.catchupProgress.fileQueue.length) {
            state.catchupProgress.allCatchupDataSent = true;
            console.log("CATCHUP: All files have been processed.");
            return { isComplete: true, newTimestamp: state.lastSentTimestamp };
        }
    }

    return { isComplete: false, newTimestamp: state.lastSentTimestamp };
}

export default runCatchupIteration;
