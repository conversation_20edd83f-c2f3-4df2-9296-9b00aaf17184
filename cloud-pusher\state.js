// Central state management - combines states.js and state/index.js

// --- Global States ---
export const STATES = {
    IDLE: 'idle',
    INITIALIZING: 'initializing',
    HANDSHAKE_FAILED: 'handshake_failed',
    CATCHUP: 'running_catchup',
    LIVE: 'running_live',
    ERROR: 'error',
    STOPPED: 'stopped',
};

// Global state variables
export const state = {
    currentGlobalState: STATES.IDLE,
    initData: null,
    lastSentTimestamp: 0,
    catchupProgress: {
        fileQueue: [],
        currentFileIndex: 0,
        currentLineInFile: 0,
        allCatchupDataSent: false,
    },
    liveModeRuntimeState: {
        lastValues: [],
        registerValidation: null,
        batchCount: 0,
        lastBatchSentTime: null,
    },
    tickServiceCancel: null,
    inMemoryFailedBatches: [],
    isTickServiceRunning: false,
};