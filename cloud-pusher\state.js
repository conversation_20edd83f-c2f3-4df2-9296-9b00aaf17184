// Central state management - combines states.js and state/index.js

// --- Global States ---
export const STATES = {
    IDLE: 'idle',
    INITIALIZING: 'initializing',
    HANDSHAKE_FAILED: 'handshake_failed',
    CATCHUP: 'running_catchup',
    LIVE: 'running_live',
    UNIFIED: 'running_unified', // New state for simultaneous live and catchup
    ERROR: 'error',
    STOPPED: 'stopped',
};

// Global state variables
export const state = {
    currentGlobalState: STATES.IDLE,
    initData: null,
    lastSentTimestamp: 0,
    catchupProgress: {
        fileQueue: [],
        currentFileIndex: 0,
        currentLineInFile: 0,
        allCatchupDataSent: false,
        lastCsvTimestampSent: 0, // Track CSV progress separately from live data
    },
    liveModeRuntimeState: {
        lastValues: [],
        registerValidation: null,
        batchCount: 0,
        lastBatchSentTime: null,
    },
    // New unified batch processing state
    unifiedBatchState: {
        pendingDataQueue: [], // Queue for data from both live and CSV sources
        lastBatchSentTime: null,
        totalBatchesSent: 0,
        liveDataCount: 0,
        csvDataCount: 0,
    },
    tickServiceCancel: null,
    inMemoryFailedBatches: [],
    isTickServiceRunning: false,
};